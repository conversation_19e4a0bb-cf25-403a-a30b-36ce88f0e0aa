import {
  Blend,
  Image,
  LoaderCircle,
  Send,
  Smile,
  Sparkles,
  Trash2,
  X,
} from 'lucide-react'
import { useCallback, useMemo, useState } from 'react'
import { useNavigate, useParams } from 'react-router'
import { cn } from '@/lib/utils'
import { useAdvancedEditStore } from '@/stores/advanced-edit'
import { type BasemapImageClassType, isImageBasemap } from '@/stores/creation'
import InputPull from './input-pull'
import { AutoTextarea } from './ui/auto-textarea'
import { Button } from './ui/button'
import { ToggleGroup, ToggleGroupItem } from './ui/toggle-group'

type AdvancedEditInputProps = {
  className?: string
  onGenerate?: () => void
}
export default function AdvancedEditInput({
  className,
  onGenerate,
}: AdvancedEditInputProps) {
  const [height, setHeight] = useState(300)
  const [open, setOpen] = useState(false)
  const { sessionid } = useParams()
  const navigate = useNavigate()
  const {
    prompt,
    setPrompt,
    foreground,
    generate,
    retexture,
    // addEstablishingShot,
    activeType,
    isGenerating,
    suggestPrompt,
    basemap,
    clearBasemap,
    removeBasemap,
    updateImageBasemapType,
    searchImageFn,
    searchQuery,
  } = useAdvancedEditStore()
  // const hasPrompt = useMemo(() => prompt.length > 0, [prompt]);
  const hasForeground = useMemo(() => !!foreground, [foreground])
  const hasImages = useMemo(() => basemap.length > 0, [basemap])
  const imageBasemap = useMemo(() => {
    const imageBasemap = basemap.filter(isImageBasemap)
    const centent = imageBasemap.filter(({ type }) => type === 'content')
    const remix = imageBasemap.filter(({ type }) => type === 'remix')
    const sref = imageBasemap.filter(({ type }) => type === 'sref')
    const cref = imageBasemap.filter(({ type }) => type === 'cref')
    return [
      { type: 'content' as const, list: remix.concat(centent) },
      { type: 'sref' as const, list: sref },
      { type: 'cref' as const, list: cref },
    ]
  }, [basemap])
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setPrompt(e.target.value)
  }
  const handleGoToSession = useCallback(
    (sessionId: string) => {
      navigate(`/advanced-edit/${sessionId}`)
    },
    [navigate]
  )
  const handleGenerate = useCallback(() => {
    if (activeType === 'edit') {
      generate(sessionid, handleGoToSession)
    } else {
      retexture(sessionid, handleGoToSession)
    }
    if (open) {
      setOpen(false)
    }
    onGenerate?.()
  }, [
    generate,
    retexture,
    onGenerate,
    activeType,
    handleGoToSession,
    sessionid,
    open,
  ])
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      // 如果是组合键，则不处理
      if (e.metaKey || e.shiftKey || e.ctrlKey) {
        return
      }
      if (e.key === 'Enter') {
        e.preventDefault()
        handleGenerate()
      }
    },
    [handleGenerate]
  )

  const handlePullOpen = useCallback(() => {
    if (open) {
      setOpen(false)
    } else {
      const h =
        document.getElementById('advanced-edit-input')?.clientHeight || 0
      setHeight(h)
      setOpen(true)
      setTimeout(() => {
        if (prompt.trim() && !open && !searchQuery.trim()) {
          searchImageFn(prompt)
        }
      }, 100)
    }
  }, [prompt, searchImageFn, searchQuery, open])

  return (
    <div className={cn('relative', className)} id="advanced-edit-input">
      <div className="flex flex-col w-full rounded-xl shadow-lg border bg-zinc-50 dark:bg-zinc-900">
        <div className="flex gap-2 p-2 pl-1">
          <AutoTextarea
            className="focus-visible:ring-0 border-none shadow-none resize-none min-h-[32px] max-h-[512px] overflow-y-auto py-1.5 px-2"
            placeholder="输入文字，按回车键开始生成图片"
            value={prompt}
            onChange={handleTextChange}
            onKeyDown={handleKeyDown}
          />
          <Button
            variant="ghost"
            size="icon"
            title="建议提示词"
            className="size-8 text-muted-foreground hidden"
            onClick={suggestPrompt}
          >
            <Sparkles className="size-6" />
          </Button>
          {/* <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                title="高级设置"
                className="size-8 text-muted-foreground"
              >
                <SlidersHorizontal className="size-6" />
              </Button>
            </PopoverTrigger>
            <PopoverContent
              side="top"
              sideOffset={14}
              align="end"
              alignOffset={-10}
              className="rounded-xl shadow-md container w-full p-3 bg-zinc-50 dark:bg-zinc-900"
            >
              <CreationSetting />
            </PopoverContent>
          </Popover> */}
          <Button
            variant="default"
            size="icon"
            title={`生成${activeType === 'edit' ? '编辑' : '转绘'}`}
            className="size-8"
            onClick={handleGenerate}
            disabled={isGenerating || !prompt.length || !hasForeground}
          >
            {isGenerating ? (
              <LoaderCircle className="size-5 animate-spin" />
            ) : (
              <Send className="size-5" />
            )}
          </Button>
        </div>
        <div className="flex border-t">
          <div className="flex flex-wrap divide-x flex-1 border-r">
            {imageBasemap.map(({ type, list }) => (
              <div
                className={cn('flex gap-1 p-2 flex-1 min-h-16 relative')}
                key={`establishing-shot-class-${type}`}
              >
                <button
                  className="absolute top-0 left-0 size-full cursor-pointer z-10"
                  onClick={handlePullOpen}
                  type="button"
                />
                <div className="text-muted-foreground pb-2">
                  {type === 'content' && (
                    <>
                      <h3 className={cn('text-sm mb-0.5')}>
                        <Image className="size-4 inline-block -mt-1 mr-1" />
                        参照图片的构图和元素
                      </h3>
                      <p className="text-xs opacity-70">风景/人物/抽象</p>
                    </>
                  )}
                  {type === 'sref' && (
                    <>
                      <h3 className="text-sm mb-0.5">
                        <Blend className="size-4 inline-block -mt-1 mr-1" />
                        参考图片的视觉风格
                      </h3>
                      <p className="text-xs opacity-70">铅笔画/油画/怀旧</p>
                    </>
                  )}
                  {type === 'cref' && (
                    <>
                      <h3 className="text-sm mb-0.5">
                        <Smile className="size-4 inline-block -mt-1 mr-1" />
                        参考角色的外观
                      </h3>
                      <p className="text-xs opacity-70">人物肖像/特写/半身像</p>
                    </>
                  )}
                </div>
                <div
                  className={cn(
                    'flex gap-0.5 ml-auto max-w-[75%] flex-wrap justify-end content-start relative z-20'
                  )}
                >
                  {list.map(({ url, type }) => (
                    <div
                      className="relative size-12 overflow-hidden rounded-md group border"
                      key={`${url}-${type}`}
                    >
                      <img
                        src={`${url}?x-oss-process=image/format,webp`}
                        alt="参考图片"
                        className="w-full h-full object-cover"
                      />
                      <Button
                        variant="secondary"
                        size="icon"
                        className="absolute z-20 top-0 right-0 size-5 p-0.5 text-muted-foreground rounded-none rounded-bl-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                        onClick={() =>
                          removeBasemap(
                            url as string,
                            type as BasemapImageClassType
                          )
                        }
                      >
                        <X className="size-4" />
                      </Button>
                      {type === 'remix' ? (
                        <div className="absolute bottom-1 right-1 text-xs text-white bg-red-500/60 backdrop-blur-sm rounded-full select-none pointer-events-none px-1.5">
                          重塑
                        </div>
                      ) : (
                        <div
                          className="absolute bottom-0 left-0 z-10 w-full py-0.5 flex items-center justify-center bg-muted/20 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                          title={type}
                        >
                          <ToggleGroup
                            type="single"
                            size="sm"
                            value={type}
                            className="gap-0"
                            onValueChange={value =>
                              updateImageBasemapType(
                                url as string,
                                type as BasemapImageClassType,
                                value as BasemapImageClassType
                              )
                            }
                          >
                            <ToggleGroupItem
                              value="content"
                              title="参照底图"
                              aria-label="Toggle content"
                              className="size-3 p-0.5 min-w-4 rounded-none rounded-l-sm text-white"
                            >
                              <Image className="!size-3" />
                            </ToggleGroupItem>
                            <ToggleGroupItem
                              value="sref"
                              title="参照风格"
                              aria-label="Toggle sref"
                              className="size-3 p-0.5 min-w-4 rounded-none text-white"
                            >
                              <Blend className="!size-3" />
                            </ToggleGroupItem>
                            <ToggleGroupItem
                              value="cref"
                              title="参照角色"
                              aria-label="Toggle cref"
                              className="size-3 p-0.5 min-w-4 rounded-none rounded-r-sm text-white"
                            >
                              <Smile className="!size-3" />
                            </ToggleGroupItem>
                          </ToggleGroup>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
          <Button
            variant="ghost"
            size="icon"
            title="清空垫图"
            className="size-8 text-muted-foreground"
            onClick={clearBasemap}
            disabled={!hasImages}
          >
            <Trash2 className="size-5" />
          </Button>
        </div>
      </div>
      <InputPull height={height} open={open} onOpenChange={setOpen} />
    </div>
  )
}
