import {
  <PERSON><PERSON>d,
  Image,
  Loader2,
  Plus,
  Send,
  Smile,
  Trash2,
  TypeOutline,
  X,
} from 'lucide-react'
import { useCallback, useMemo, useState } from 'react'
import { useLocation, useNavigate } from 'react-router'
import { cn } from '@/lib/utils'
import {
  ARTICLE_LENGTH_LIST,
  ARTICLE_TONE_LIST,
  ARTICLE_TYPE_LIST,
  type BasemapImageClassType,
  isArticleBasemap,
  isImageBasemap,
  isVideoBasemap,
  useCreationStore,
} from '@/stores/creation'
import CreationInputPull from './creation-input-pull'
import { AutoTextarea } from './ui/auto-textarea'
import { Button } from './ui/button'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from './ui/select'
import { ToggleGroup, ToggleGroupItem } from './ui/toggle-group'
import { <PERSON>lt<PERSON>, TooltipContent, TooltipTrigger } from './ui/tooltip'

type CreationInputProps = {
  className?: string
  onGenerate?: () => void
}
export default function CreationInput({
  className,
  onGenerate,
}: CreationInputProps) {
  const [height, setHeight] = useState(300)
  const [open, setOpen] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const currentPath = location.pathname
  const {
    prompt,
    setPrompt,
    generate,
    basemap,
    removeBasemap,
    updateImageBasemapType,
    clearBasemap,
    clearImageRemixBasemap,
    optimizationPromptFn,
    promptLoading,
    generationLoading,
    generationType,
    clearVideoBasemap,
    setSearchQuery,
    searchImageFn,
    setArticle,
    searchQuery,
  } = useCreationStore()
  const hasPrompt = useMemo(() => prompt.length > 0, [prompt])
  const videoBasemap = useMemo(() => basemap.find(isVideoBasemap), [basemap])
  const articleBasemap = useMemo(
    () => basemap.find(isArticleBasemap),
    [basemap]
  )
  const generationDisabled = useMemo(() => {
    if (generationType === 'video') {
      return !videoBasemap
    }
    return !hasPrompt
  }, [generationType, hasPrompt, videoBasemap])
  const hasImages = useMemo(() => {
    if (generationType === 'video') {
      return !!videoBasemap
    }

    return basemap.length > 0
  }, [basemap, generationType, videoBasemap])
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setPrompt(e.target.value)
  }
  const handleGoBack = useCallback(() => {
    navigate(-1)
  }, [navigate])
  const handleGenerate = useCallback(() => {
    if (open) {
      setOpen(false)
    }
    generate(() => {
      if (currentPath !== '/') {
        handleGoBack()
      }
    })
    onGenerate?.()
  }, [generate, onGenerate, currentPath, handleGoBack, open])
  const handleOptimizationPrompt = useCallback(() => {
    optimizationPromptFn()
  }, [optimizationPromptFn])
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      // 如果是组合键，则不处理
      if (e.metaKey || e.shiftKey || e.ctrlKey) {
        return
      }
      if (e.key === 'Enter') {
        e.preventDefault()
        handleGenerate()
      }
    },
    [handleGenerate]
  )
  const imageBasemap = useMemo(() => {
    const imageBasemap = basemap.filter(isImageBasemap)
    const centent = imageBasemap.filter(({ type }) => type === 'content')
    const remix = imageBasemap.filter(({ type }) => type === 'remix')
    const sref = imageBasemap.filter(({ type }) => type === 'sref')
    const cref = imageBasemap.filter(({ type }) => type === 'cref')

    return [
      {
        type: 'content',
        list: remix.concat(centent),
      },
      {
        type: 'sref',
        list: sref,
      },
      {
        type: 'cref',
        list: cref,
      },
    ]
  }, [basemap])
  const generationText = useMemo(() => {
    if (generationType === 'video') {
      return `${videoBasemap?.type === 'video_extend' ? '延长' : '生成'}视频`
    }

    if (generationType === 'article') {
      return '生成文章'
    }

    const hasRemixStyle = basemap.some(({ type }) => type === 'remix')

    return hasRemixStyle ? '重塑图片' : '生成图片'
  }, [generationType, videoBasemap, basemap])
  const handlePullOpen = useCallback(() => {
    if (open) {
      setOpen(false)
    } else {
      const h = document.getElementById('creation-input')?.clientHeight || 0
      setHeight(h)
      setOpen(true)
      setTimeout(() => {
        if (prompt.trim() && !open && !searchQuery.trim()) {
          setSearchQuery(prompt)
          searchImageFn()
        }
      }, 100)
    }
  }, [setSearchQuery, prompt, searchImageFn, searchQuery, open])

  return (
    <>
      <div className={cn('relative w-full', className)} id="creation-input">
        <div className="flex flex-col w-full rounded-xl rounded-tl-none shadow-lg border bg-zinc-50 dark:bg-zinc-900 relative">
          <div
            className={cn('flex gap-2 py-2 pr-2 pl-1', {
              'pl-16': generationType === 'video',
            })}
          >
            {generationType === 'video' && (
              <div className="absolute top-1/2 -translate-y-1/2 left-0 rotate-[-5deg] z-10 -translate-x-4 flex items-center">
                {videoBasemap ? (
                  <div className="group flex justify-center items-center relative size-12 overflow-hidden rounded-lg border border-muted-foreground/50 bg-zinc-50 dark:bg-zinc-900 hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors duration-300 z-10">
                    {videoBasemap.type === 'video_first_frame' && (
                      <>
                        <Button
                          variant="secondary"
                          size="icon"
                          className="absolute z-20 top-0 right-0 size-5 p-0.5 text-muted-foreground rounded-none rounded-bl-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                          onClick={clearVideoBasemap}
                        >
                          <X className="size-4" />
                        </Button>
                        <img
                          src={videoBasemap.url}
                          className="w-full h-full object-cover"
                          alt={videoBasemap.url}
                        />
                      </>
                    )}
                    {videoBasemap.type === 'video_extend' && (
                      <>
                        <Button
                          variant="secondary"
                          size="icon"
                          className="absolute z-20 top-0 right-0 size-5 p-0.5 text-muted-foreground rounded-none rounded-bl-lg cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                          onClick={clearVideoBasemap}
                        >
                          <X className="size-4" />
                        </Button>
                        <video
                          src={videoBasemap.url}
                          className="w-full h-full object-cover"
                          autoPlay
                          muted
                          loop
                          controls={false}
                        />
                      </>
                    )}
                  </div>
                ) : (
                  <button
                    className="flex justify-center items-center size-12 rounded-lg border border-dashed border-muted-foreground/50 bg-zinc-50 dark:bg-zinc-900 cursor-pointer hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors duration-300 z-10"
                    onClick={handlePullOpen}
                    type="button"
                  >
                    <Plus className="size-4" />
                  </button>
                )}
                <span className="text-xs bg-muted origin-top-left rotate-[5deg] px-1.5 font-light scale-90 py-0.5 rounded-r-lg border border-muted-foreground/20 border-l-0 leading-[14px]">
                  视频
                  <br />
                  {videoBasemap?.type === 'video_extend' ? '延长' : '首帧'}
                </span>
              </div>
            )}
            <AutoTextarea
              className="focus-visible:ring-0 border-none shadow-none resize-none min-h-[32px] py-1.5 px-2 max-h-[512px] overflow-y-auto"
              placeholder={`输入文字，按回车键开始${generationText}`}
              value={prompt}
              maxLength={2500}
              onChange={handleTextChange}
              onKeyDown={handleKeyDown}
            />
            {generationType !== 'article' && hasPrompt && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="size-8 text-muted-foreground"
                    onClick={handleOptimizationPrompt}
                    disabled={promptLoading}
                  >
                    {promptLoading ? (
                      <Loader2 className="size-5 animate-spin" />
                    ) : (
                      <TypeOutline className="size-5" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{promptLoading ? '优化中...' : '优化提示词'}</p>
                </TooltipContent>
              </Tooltip>
            )}
            {/* <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="icon" title="高级设置" className="size-8 text-muted-foreground">
                <SlidersHorizontal className="size-5" />
              </Button>
            </PopoverTrigger>
            <PopoverContent
              side="top"
              sideOffset={14}
              align="end"
              alignOffset={-10}
              className="rounded-xl shadow-md container w-full p-3 bg-zinc-50 dark:bg-zinc-900"
            >
              <CreationSetting />
            </PopoverContent>
          </Popover> */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="default"
                  size="icon"
                  className="size-8"
                  onClick={handleGenerate}
                  disabled={generationLoading || generationDisabled}
                >
                  {generationLoading ? (
                    <Loader2 className="size-5 animate-spin" />
                  ) : (
                    <Send className="size-5" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{generationLoading ? '生成中...' : generationText}</p>
              </TooltipContent>
            </Tooltip>
          </div>
          {generationType === 'image' && (
            <div className="flex border-t">
              <div className="flex flex-wrap divide-x flex-1 border-r">
                {imageBasemap.map(({ type, list }, index) => (
                  <div
                    className={cn('flex gap-1 p-2 flex-1 min-h-16 relative')}
                    key={`creation-input-class-${index}-${type}`}
                  >
                    <button
                      className="absolute top-0 left-0 size-full cursor-pointer z-10"
                      onClick={handlePullOpen}
                      type="button"
                    />
                    <div className="text-muted-foreground pb-2">
                      {type === 'content' && (
                        <>
                          <h3 className={cn('text-sm mb-0.5')}>
                            <Image className="size-4 inline-block -mt-1 mr-1" />
                            参照图片的构图和元素
                          </h3>
                          <p className="text-xs opacity-70">
                            正版图垫图（生成更合规）：风景/人物/抽象
                          </p>
                        </>
                      )}
                      {type === 'sref' && (
                        <>
                          <h3 className="text-sm mb-0.5">
                            <Blend className="size-4 inline-block -mt-1 mr-1" />
                            参考图片的视觉风格
                          </h3>
                          <p className="text-xs opacity-70">
                            正版图垫图（生成更合规）：铅笔画/油画/怀旧
                          </p>
                        </>
                      )}
                      {type === 'cref' && (
                        <>
                          <h3 className="text-sm mb-0.5">
                            <Smile className="size-4 inline-block -mt-1 mr-1" />
                            参考角色的外观
                          </h3>
                          <p className="text-xs opacity-70">
                            正版图垫图（生成更合规）：人物肖像/特写/半身像
                          </p>
                        </>
                      )}
                    </div>
                    <div
                      className={cn(
                        'flex gap-0.5 ml-auto max-w-[75%] flex-wrap justify-end'
                      )}
                    >
                      {/* {remixEstablishingShot && type === 'content' && (
                        <div className="relative size-12 overflow-hidden rounded-md group border z-20">
                          <img src={remixEstablishingShot.url} className="w-full h-full object-cover" />
                          <Button
                            variant="ghost"
                            size="icon"
                            className="absolute z-20 top-0 right-0 size-5 p-1 text-muted-foreground rounded-none rounded-bl-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                            onClick={clearRemixEstablishingShot}
                          >
                            <X className="size-4" />
                          </Button>
                          <div className="absolute bottom-1 right-1 text-xs text-white bg-red-500/80 rounded-full select-none pointer-events-none px-1.5">
                            重塑
                          </div>
                        </div>
                      )} */}
                      {list.map(({ url, type }, itemIndex) => (
                        <div
                          className="relative size-12 overflow-hidden rounded-md group border"
                          key={`${url}-${index}-class-${type}-${itemIndex}-${url}`}
                        >
                          <img
                            src={`${url}?x-oss-process=image/format,webp`}
                            className="w-full h-full object-cover"
                            alt={url}
                          />
                          <Button
                            variant="ghost"
                            size="icon"
                            className="absolute z-20 top-0 right-0 size-5 p-0.5 text-muted-foreground rounded-none rounded-bl-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                            onClick={() =>
                              removeBasemap(
                                url as string,
                                type as BasemapImageClassType
                              )
                            }
                          >
                            <X className="size-4" />
                          </Button>
                          {type === 'remix' ? (
                            <div className="absolute bottom-1 right-1 text-xs text-white bg-red-500/60 backdrop-blur-sm rounded-full select-none pointer-events-none px-1.5">
                              重塑
                            </div>
                          ) : (
                            <div
                              className="absolute bottom-0 left-0 z-10 w-full py-0.5 flex items-center justify-center bg-muted/20 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                              title={type}
                            >
                              <ToggleGroup
                                type="single"
                                size="sm"
                                value={type}
                                className="gap-0"
                                onValueChange={value =>
                                  updateImageBasemapType(
                                    url as string,
                                    type as BasemapImageClassType,
                                    value as BasemapImageClassType
                                  )
                                }
                              >
                                <ToggleGroupItem
                                  value="content"
                                  title="参照底图"
                                  aria-label="Toggle content"
                                  className="size-3 p-0.5 min-w-4 rounded-none rounded-l-sm"
                                >
                                  <Image className="!size-3" />
                                </ToggleGroupItem>
                                <ToggleGroupItem
                                  value="sref"
                                  title="参照风格"
                                  aria-label="Toggle sref"
                                  className="size-3 p-0.5 min-w-4 rounded-none"
                                >
                                  <Blend className="!size-3" />
                                </ToggleGroupItem>
                                <ToggleGroupItem
                                  value="cref"
                                  title="参照角色"
                                  aria-label="Toggle cref"
                                  className="size-3 p-0.5 min-w-4 rounded-none rounded-r-sm"
                                >
                                  <Smile className="!size-3" />
                                </ToggleGroupItem>
                              </ToggleGroup>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
              <Button
                variant="ghost"
                size="icon"
                title="清空垫图"
                className="size-8 text-muted-foreground"
                onClick={() => {
                  clearBasemap()
                  clearImageRemixBasemap()
                }}
                disabled={!hasImages}
              >
                <Trash2 className="size-5" />
              </Button>
            </div>
          )}
          {generationType === 'article' && (
            <div className="flex px-2 pb-2 gap-2">
              <Select
                value={articleBasemap?.class}
                onValueChange={value => setArticle({ class: value })}
              >
                <SelectTrigger className="w-40">
                  类型：
                  <SelectValue placeholder="选择类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>文章类型</SelectLabel>
                    {ARTICLE_TYPE_LIST.map(({ value, label }) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
              <Select
                value={articleBasemap?.lenght}
                onValueChange={value => setArticle({ lenght: value })}
              >
                <SelectTrigger className="w-32">
                  长度：
                  <SelectValue placeholder="请选择" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>文章长度</SelectLabel>
                    {ARTICLE_LENGTH_LIST.map(({ value, label }) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
              <Select
                value={articleBasemap?.tone}
                onValueChange={value => setArticle({ tone: value })}
              >
                <SelectTrigger className="w-40">
                  语气：
                  <SelectValue placeholder="选择语气" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>文章语气</SelectLabel>
                    {ARTICLE_TONE_LIST.map(({ value, label }) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
      </div>
      <CreationInputPull open={open} onOpenChange={setOpen} height={height} />
    </>
  )
}
