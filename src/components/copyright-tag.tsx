import { Copyright } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip'

type CopyrightTagProps = {
  is_copyright: number
  className?: string
  align?: 'start' | 'end'
}
export default function CopyrightTag({
  is_copyright,
  className,
  align = 'start',
}: CopyrightTagProps) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div
          className={cn(
            'relative p-1.5 bg-foreground/20 backdrop-blur rounded-full text-white',
            className,
            {
              'bg-yellow-400/90': is_copyright,
            }
          )}
        >
          <Copyright className="size-4" />
          {!is_copyright && (
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[70%] h-[1.5px] bg-current rotate-45 z-10" />
          )}
        </div>
      </TooltipTrigger>
      <TooltipContent align={align} side="bottom">
        <p>
          {is_copyright
            ? '此资源基于视觉中国素材生成，已获基础商用授权'
            : '未使用版权图库，商用需自行核查风险'}
        </p>
      </TooltipContent>
    </Tooltip>
  )
}
