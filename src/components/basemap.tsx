import { <PERSON>lend, Film, Image, Smile, Video } from 'lucide-react'
import { useMemo } from 'react'
import { cn } from '@/lib/utils'
import {
  ARTICLE_LENGTH_LIST,
  type BasemapType,
  type GenerationType,
  isArticleBasemap,
  useCreationStore,
} from '@/stores/creation'
import { useImageStore } from '@/stores/image'
import { Button } from './ui/button'

type BasemapProps = {
  basemap: BasemapType[]
  className?: string
  boxType?: GenerationType
}
export default function Basemap({
  basemap,
  className,
  boxType = 'image',
}: BasemapProps) {
  const { setArticle } = useCreationStore()
  const { setImageUrl } = useImageStore()
  const basemapClassList = useMemo(() => {
    const result: BasemapType[][] = []

    if (!basemap) {
      return result
    }

    const centent = basemap.filter(({ type }) => type === 'content')
    const sref = basemap.filter(({ type }) => type === 'sref')
    const cref = basemap.filter(({ type }) => type === 'cref')
    const remix = basemap.filter(({ type }) => type === 'remix')
    const video_extend = basemap.filter(({ type }) => type === 'video_extend')
    const video_first_frame = basemap.filter(
      ({ type }) => type === 'video_first_frame'
    )

    if (centent.length > 0) {
      result.push(centent)
    }
    if (sref.length > 0) {
      result.push(sref)
    }
    if (cref.length > 0) {
      result.push(cref)
    }
    if (remix.length > 0) {
      result.push(remix)
    }
    if (video_extend.length > 0) {
      result.push(video_extend)
    }
    if (video_first_frame.length > 0) {
      result.push(video_first_frame)
    }

    return result
  }, [basemap])
  const article = useMemo(() => basemap.find(isArticleBasemap), [basemap])

  return (
    <div className={cn('flex flex-wrap divide-x gap-y-1', className)}>
      {(boxType === 'image' || boxType === 'video') &&
        basemapClassList.map((basemapClass, index) => (
          <div
            className="flex flex-wrap gap-1 px-2"
            key={`basemap-class-${index}-${basemapClass.length}`}
          >
            {basemapClass.map(({ url, type }) => (
              <button
                className="relative size-12 overflow-hidden rounded-md group border"
                key={`${url}-${index}-class-${type}`}
                onClick={() => url && setImageUrl(url)}
                type="button"
              >
                {boxType === 'image' && (
                  <img
                    src={`${url}?x-oss-process=image/format,webp`}
                    alt={url}
                    className="w-full h-full object-cover"
                  />
                )}
                {boxType === 'video' && type === 'video_extend' && (
                  <video
                    src={url}
                    className="w-full h-full object-cover"
                    muted
                    playsInline
                    controls={false}
                  />
                )}
                {boxType === 'video' && type === 'video_first_frame' && (
                  <img
                    src={`${url}?x-oss-process=image/format,webp`}
                    alt={url}
                    className="w-full h-full object-cover"
                  />
                )}
                <div
                  className={cn(
                    'absolute bottom-0.5 right-0.5 z-10 size-4 leading-3 bg-foreground/80 text-background backdrop-blur-sm shadow-sm rounded-sm p-0.5',
                    {
                      'h-4 w-auto py-0': type === 'remix',
                    }
                  )}
                >
                  {type === 'content' && <Image className="size-3" />}
                  {type === 'sref' && <Blend className="size-3" />}
                  {type === 'cref' && <Smile className="size-3" />}
                  {type === 'remix' && (
                    <span className="text-[10px]">重塑</span>
                  )}
                  {type === 'video_extend' && <Video className="size-3" />}
                  {type === 'video_first_frame' && <Film className="size-3" />}
                </div>
              </button>
            ))}
          </div>
        ))}
      {boxType === 'article' && article && (
        <div className="flex flex-wrap gap-1 px-2">
          {Object.entries(article)
            .filter(([key]) => key !== 'prompt' && key !== 'type')
            .map(([key, value]) => (
              <Button
                variant="outline"
                key={key}
                className="text-xs"
                size="sm"
                onClick={() => setArticle({ [key]: value })}
              >
                {
                  {
                    class: '类型',
                    lenght: '长度',
                    tone: '风格',
                  }[key]
                }
                :{' '}
                {key === 'lenght'
                  ? ARTICLE_LENGTH_LIST.find(({ value: v }) => v === value)
                      ?.label
                  : value}
              </Button>
            ))}
        </div>
      )}
    </div>
  )
}
