import { imageProportions } from '@/config'
import { cn } from '@/lib/utils'
import { useCreationStore } from '@/stores/creation'
import { Label } from './ui/label'

export const CreationSetting = () => {
  const { setAspectRatio, aspectRatio } = useCreationStore()

  return (
    <div className="w-full flex gap-2">
      <div className="grid gap-2">
        <Label>图片比例</Label>
        <div className="grid grid-cols-4 border rounded-lg overflow-hidden">
          {imageProportions.map(({ name, id }) => (
            <div
              className={cn(
                'relative cursor-pointer bg-muted text-center py-2 px-3 border -m-[1px] hover:bg-primary hover:text-primary-foreground',
                {
                  'bg-primary': aspectRatio === name,
                  'text-primary-foreground': aspectRatio === name,
                }
              )}
              key={id}
              onClick={() => setAspectRatio(name)}
            >
              <span className="select-none text-xs">{name}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
