/** biome-ignore-all lint/suspicious/noArrayIndexKey: index is intentionally used */
import { Trash2 } from 'lucide-react'
import { useCallback, useEffect, useMemo } from 'react'
import { useSearchParams } from 'react-router'
import { cn } from '@/lib/utils'
import { type JobItemType, useAdvancedEditStore } from '@/stores/advanced-edit'
import { DiffusionStatus, JobType } from '@/stores/creation'
import { Button } from './ui/button'

type AdvancedJobBoxProps = {
  job: JobItemType
}
export default function AdvancedJobBox({ job }: AdvancedJobBoxProps) {
  const {
    id,
    status,
    cover_url,
    canvas,
    type = JobType.DEFAULT,
    urls = [],
  } = job
  const [searchParams, setSearchParams] = useSearchParams()
  const { no, jobId } = useMemo(() => {
    const no = searchParams.get('index')
    const jobId = searchParams.get('jobId')

    return {
      no: no ? Number(no) : undefined,
      jobId: jobId || undefined,
    }
  }, [searchParams])
  const { getJobInfo, updateJob, removeJob } = useAdvancedEditStore()
  const loading = useMemo(
    () =>
      !(
        status === DiffusionStatus.COMPLETED ||
        status === DiffusionStatus.FAILED
      ),
    [status]
  )
  const { aspectRatio } = useMemo(() => {
    const { width = 1, height = 1 } = canvas || {}
    const aspectRatio = width / height

    return { aspectRatio }
  }, [canvas])
  const handleGetJobInfo = useCallback(async () => {
    // 如果已经有 urls 或者任务已完成/失败，跳过请求
    if (
      urls.length > 0 ||
      status === DiffusionStatus.COMPLETED ||
      status === DiffusionStatus.FAILED
    ) {
      return
    }
    const { status_code, data, message } = await getJobInfo(id)

    if (status_code === 1) {
      const {
        status: jobStatus,
        urls: jobUrls,
        seed,
        audits,
        comment,
        updated_at,
        // session_id,
      } = data

      if (jobStatus === DiffusionStatus.PROCESSING) {
        // 只有在组件仍然挂载时才设置定时器
        setTimeout(() => {
          handleGetJobInfo()
        }, 2000)
        return
      } else if (jobStatus === DiffusionStatus.COMPLETED) {
        console.info('任务完成，更新 job 数据：', { id, jobStatus, jobUrls })
        updateJob(id, {
          status: jobStatus,
          urls: jobUrls,
          seed,
          audits,
          updated_at,
        })
        // const firstImage = jobUrls[0]
        // const nextFg = {
        //   type: 'image' as const,
        //   width: worktop?.width || firstImage.width,
        //   height: worktop?.height || firstImage.height,
        //   aspectRatio: firstImage.width / firstImage.height,
        //   rmbgBase64: undefined,
        //   id: firstImage.id,
        //   rotate: 0,
        //   url: firstImage.url,
        //   x: 0,
        //   y: 0,
        // }
        // setForeground(nextFg, { resetOrigin: true })
        // 使用 setTimeout 确保 updateJob 完成后再设置搜索参数
        setTimeout(() => {
          console.info('设置搜索参数：', { jobId: id, index: '0' })
          setSearchParams({
            jobId: id,
            index: '0',
          })
        }, 100) // 100ms 延迟确保状态同步
        // window.location.href = `/advanced-edit/${session_id}?jobId=${id}&index=0`
      } else if (jobStatus === DiffusionStatus.FAILED) {
        updateJob(id, {
          status: jobStatus,
          comment,
          audits,
          updated_at,
        })
      }
    } else if (status_code === 0) {
      updateJob(id, {
        status: DiffusionStatus.FAILED,
        comment: message,
        updated_at: new Date().toISOString(),
      })
    }
  }, [getJobInfo, id, updateJob, urls.length, status, setSearchParams]) // 只依赖 urls.length 而不是整个 urls 数组

  useEffect(() => {
    handleGetJobInfo()
  }, [handleGetJobInfo]) // 依赖 handleGetJobInfo，但由于 useCallback 优化，不会造成无限循环
  // const clearTimer = useInterval(
  //   () => {
  //     handleGetJobInfo()
  //   },
  //   status === DiffusionStatus.COMPLETED || status === DiffusionStatus.FAILED
  //     ? undefined
  //     : 2000
  // )

  // useUnmount(() => {
  //   clearTimer()
  // })

  return (
    <div className={cn('flex gap-1 pb-4 xl:pb-2')}>
      <div className="w-14 relative">
        <button
          type="button"
          className="w-14 border-none select-none text-xs relative overflow-hidden bg-white-box dark:bg-black-box bg-8px rounded shadow-md cursor-pointer hover:shadow-lg hover:scale-110 hover:z-10 transition duration-300"
          style={{
            aspectRatio,
          }}
          onClick={() => {
            // viewJobImage(id)
            setSearchParams({
              jobId: id,
            })
          }}
        >
          {type === JobType.RETEXTURE && (
            <span className="absolute top-0 left-0 bg-black text-white text-xs px-1 py-0.5 rounded-br-lg">
              转绘
            </span>
          )}
          {no === undefined && jobId === id && (
            <div className="absolute top-0 left-0 w-full h-full border-2 border-blue-500 z-10" />
          )}
          <img src={cover_url} alt="操作图片" className="contain-layout" />
        </button>
      </div>
      {status === DiffusionStatus.FAILED ? (
        <div className="flex-1 flex gap-4 justify-center items-center">
          <p className="text-xs text-muted-foreground text-center">生成失败</p>
          <Button
            variant="outline"
            size="icon"
            onClick={() => {
              removeJob(id)
            }}
          >
            <Trash2 />
          </Button>
        </div>
      ) : (
        <div
          className={cn('flex-1 gap-0.5 grid', {
            'grid-cols-2': aspectRatio > 1,
            'grid-cols-4': aspectRatio <= 1,
            'animate-pulse': loading,
          })}
        >
          {loading
            ? [...Array(4)].map((_, index) => (
                <div
                  key={`${index}-advanced-job-box`}
                  className={cn(
                    'w-full h-full overflow-hidden bg-zinc-200 dark:bg-zinc-800 animate-pulse flex justify-center items-center',
                    {
                      'rounded-l':
                        index === 0 || (aspectRatio > 1 && index % 2 === 0),
                      'rounded-r':
                        (aspectRatio > 1 && index % 2 === 1) || index === 3,
                    }
                  )}
                  style={{
                    aspectRatio,
                  }}
                >
                  <p className="text-muted-foreground text-xs text-center select-none scale-75 w-full whitespace-nowrap">
                    生成中
                  </p>
                </div>
              ))
            : urls.map(({ url, thumbnail }, index) => (
                <button
                  type="button"
                  key={`${thumbnail || url}-${index}`}
                  className={cn(
                    'relative border-none cursor-pointer overflow-hidden bg-zinc-200 dark:bg-zinc-800 shadow hover:shadow-lg hover:scale-110 hover:z-10 transition duration-300',
                    {
                      'rounded-l':
                        index === 0 || (aspectRatio > 1 && index % 2 === 0),
                      'rounded-r':
                        index === urls.length - 1 ||
                        (aspectRatio > 1 && index % 2 === 1),
                    }
                  )}
                  style={{
                    aspectRatio,
                  }}
                  onClick={() => {
                    setSearchParams({
                      jobId: id,
                      index: index.toString(),
                    })
                  }}
                >
                  {no === index && jobId === id && (
                    <div className="absolute top-0 left-0 w-full h-full border-2 border-blue-500 z-10" />
                  )}
                  <img
                    src={thumbnail || url}
                    alt={`第${index + 1}张`}
                    loading="lazy"
                    className="contain-layout"
                  />
                </button>
              ))}
        </div>
      )}
    </div>
  )
}
