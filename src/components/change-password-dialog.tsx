import { zodResolver } from '@hookform/resolvers/zod'
import { X } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { useCommonStore } from '@/stores/common'
import { Button } from './ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from './ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from './ui/form'
import { Input } from './ui/input'

const formSchema = z
  .object({
    oldPassword: z.string().min(6, {
      message: '密码至少 6 个字符',
    }),
    newPassword: z
      .string()
      .min(8, {
        message: '密码至少 8 个字符',
      })
      .max(16, {
        message: '密码最多 16 个字符',
      })
      .regex(/^\S+$/, {
        message: '密码不能包含空格',
      })
      .refine(
        value => {
          const hasUppercase = /[A-Z]/.test(value)
          const hasLowercase = /[a-z]/.test(value)
          const hasNumber = /[0-9]/.test(value)
          return hasUppercase && hasLowercase && hasNumber
        },
        {
          message: '密码必须包含大写字母、小写字母和数字',
        }
      ),
    confirmNewPassword: z
      .string()
      .min(8, {
        message: '密码至少 8 个字符',
      })
      .max(16, {
        message: '密码最多 16 个字符',
      }),
  })
  .refine(data => data.newPassword !== data.oldPassword, {
    message: '新密码不能与旧密码相同',
    path: ['newPassword'],
  })
  .refine(data => data.confirmNewPassword === data.newPassword, {
    message: '确认密码与新密码不一致',
    path: ['confirmNewPassword'],
  })

type ChangePasswordDialogProps = {
  open: boolean
  onOpenChange: (open: boolean) => void
}
export default function ChangePasswordDialog({
  open = false,
  onOpenChange,
}: ChangePasswordDialogProps) {
  const { changePassword } = useCommonStore()
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      oldPassword: '',
      newPassword: '',
      confirmNewPassword: '',
    },
    mode: 'onBlur',
  })
  const onSubmit = form.handleSubmit(async data => {
    await changePassword(data.oldPassword, data.newPassword)
    onOpenChange(false)
  })

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="">
        <DialogHeader>
          <DialogTitle>修改密码</DialogTitle>
          <p className="text-sm text-muted-foreground">
            请确保您输入的旧密码正确，新密码符合要求
          </p>
        </DialogHeader>
        <DialogClose asChild>
          <Button
            variant="outline"
            size="icon"
            className="absolute top-2 right-2 z-50"
            onClick={() => onOpenChange(false)}
          >
            <X className="size-4" />
          </Button>
        </DialogClose>
        <Form {...form}>
          <form onSubmit={onSubmit} className="space-y-4">
            <FormField
              control={form.control}
              name="oldPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>旧密码</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="请输入您的旧密码"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                  <FormDescription>
                    请输入您的旧密码，以验证身份
                  </FormDescription>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="newPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>新密码</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="8-16位，包含大写字母、小写字母和数字"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                  <FormDescription>
                    密码必须至少 8
                    个字符，且包含字母、数字、符号中的至少两种类型
                  </FormDescription>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="confirmNewPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>确认新密码</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="请再次输入您的新密码"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                  {/* <FormDescription>请再次输入您的新密码</FormDescription> */}
                </FormItem>
              )}
            />
            <Button type="submit" className="w-full">
              修改密码
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
