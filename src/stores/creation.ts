import dayjs from 'dayjs'
import customParseFormat from 'dayjs/plugin/customParseFormat' // ES 2015
import { shake, unique } from 'radash'
import { toast } from 'sonner'
import { create } from 'zustand'
import {
  calculateRatio,
  creationScrollToBottom,
  PromptCommandParser,
  webapi,
} from '@/lib/utils'
import 'dayjs/locale/zh-cn'
import type { UploadDataType } from './advanced-edit'

dayjs.locale('zh-cn')
dayjs.extend(customParseFormat)

export type CreationItemType = {
  id: string
  prompt: string
  image: string
}
export const DEFAULT_ASPECT_RATIO = '1:1'

/**
 * 0: 新创建
 * 1: 执行中
 * 2: 执行成功
 * 3: 失败
 */
export enum DiffusionStatus {
  NEW = 0,
  PROCESSING = 1,
  COMPLETED = 2,
  FAILED = 3,
}

/**
 * 0: 默认
 * 1: 变化
 * 2: 高清
 * 3: 重塑
 * 4: 延展
 * 5: 缩放
 * 6: 移除背景
 * 7: 视频生成
 * 8: 视频延长
 * 9: 视频高清
 * 10: 文章生成
 * 11: 转绘
 * 12: 高级编辑
 */
export enum JobType {
  DEFAULT = 0,
  VARIATION = 1,
  UPSCALE = 2,
  REMIX = 3,
  PAN = 4,
  OUTPAINT = 5,
  REMOVE_BACKGROUND = 6,
  VIDEO_GENERATION = 7,
  VIDEO_EXTEND = 8,
  VIDEO_UPSCALE = 9,
  ARTICLE_GENERATION = 10,
  RETEXTURE = 11,
  ADVANCED_EDIT = 12,
}

export type JobItemType = {
  id: string
  incr_id: number
  text: string
  status: DiffusionStatus
  type: JobType
  typeValue?: number | string
  seed?: string
  comment?: string
  urls?: UploadDataType[]
  created_at?: string
  updated_at?: string
  is_copyright?: number
  article?: string
  basemap?: BasemapType[]
  audits?: string[]
}

export type VCGImageType = {
  id: number
  title: string
  url: string
  width: number
  height: number
}

export type VCGImageDataItemType = {
  id: number
  title: string
  oss800: string
  picWidth: number
  picHeight: number
}

type UpdateJobItemType = {
  status: DiffusionStatus
  seed?: string
  comment?: string
  urls?: UploadDataType[]
  updatedAt?: string
  article?: string
  audits?: string[]
}

export type DiffusionDataType = {
  incr_id: number
  id: string
  text: string
  status: DiffusionStatus
  seed: string
  type: JobType
  created_at: string
  updated_at: string
  is_copyright: number
  basemap: BasemapType[]
  comment: string
  urls: UploadDataType[]
  audits: string[]
  article?: string
}

export type ResponseType<T = unknown> = {
  status_code: number
  message: string
  data: T
}

export type VIDEO_EXTEND_TYPE_NAME = 'video_extend'
export type VIDEO_FIRST_FRAME_TYPE_NAME = 'video_first_frame'

export type BasemapImageClassType = 'sref' | 'cref' | 'content' // 风格、人脸、内容

export type BasemapVideoClassType =
  | VIDEO_EXTEND_TYPE_NAME
  | VIDEO_FIRST_FRAME_TYPE_NAME

export type motionType = 'low' | 'high'

export type GenerationType = 'image' | 'video' | 'article'

export interface BasemapBaseType {
  url: string
  is_copyright: number
}

interface BasemapImageType extends BasemapBaseType {
  type: BasemapImageClassType
  width?: number
  height?: number
}

export interface VCGImageBasemapType extends BasemapImageType {
  vcgId: number
}

export interface MJImageBasemapType extends BasemapImageType {
  jobId: string
  imageNo: number
}

export interface ImageRemixBasemapType extends BasemapBaseType {
  type: 'remix'
  jobId: string
  imageNo: number
  mode: number
}

interface BasemapVideoType extends BasemapBaseType {
  width?: number
  height?: number
}

export interface VideoExtendBasemapType extends BasemapVideoType {
  type: VIDEO_EXTEND_TYPE_NAME
  jobId: string
  videoNo: number
  motion: motionType
}

export interface VideoGenerationBasemapImageType extends BasemapVideoType {
  type: VIDEO_FIRST_FRAME_TYPE_NAME
}

export interface VideoGenerationVCGBasemapType
  extends VideoGenerationBasemapImageType {
  vcgId: number
}

export interface VideoGenerationMJBasemapType
  extends VideoGenerationBasemapImageType {
  jobId: string
  imageNo: number
}

export const ARTICLE_TYPE_LIST = [
  {
    value: '营销文案',
    label: '营销文案',
  },
]

export const ARTICLE_LENGTH_LIST = [
  {
    value: '100 字',
    label: '短',
  },
  {
    value: '400 字',
    label: '中',
  },
  {
    value: '800 字',
    label: '长',
  },
]

export const ARTICLE_TONE_LIST = [
  {
    value: '专业严谨',
    label: '专业严谨',
  },
  {
    value: '亲切友好',
    label: '亲切友好',
  },
  {
    value: '激励说服',
    label: '激励说服',
  },
  {
    value: '风趣幽默',
    label: '风趣幽默',
  },
  {
    value: '清晰直接',
    label: '清晰直接',
  },
]

export interface ArticleGenerationBasemapType {
  class?: string
  lenght?: string
  tone?: string
  prompt?: string
}

export type ArticleBasemapType = {
  type: 'article'
  class: string
  lenght: string
  tone: string
  prompt?: string
  url?: string
  is_copyright?: number
}

export const ARTICLE_DEFAULT_DATA = {
  class: '营销文案',
  lenght: '400 字',
  tone: '专业严谨',
  prompt: '',
}

// export type BasemapType = VCGImageBasemapType | MJImageBasemapType | ImageRemixBasemapType | VideoExtendBasemapType | VideoGenerationVCGBasemapType | VideoGenerationMJBasemapType | ArticleBasemapType;

export type ImageBasemapType =
  | VCGImageBasemapType
  | MJImageBasemapType
  | ImageRemixBasemapType

export type VideoBasemapType =
  | VideoExtendBasemapType
  | VideoGenerationVCGBasemapType
  | VideoGenerationMJBasemapType

export type BasemapType =
  | ImageBasemapType
  | VideoBasemapType
  | ArticleBasemapType

export type VideoGenerationParameter =
  | VideoGenerationVCGBasemapType
  | VideoGenerationMJBasemapType

export function isArticleBasemap(
  basemap: BasemapType
): basemap is ArticleBasemapType {
  return basemap.type === 'article'
}

export function isVideoBasemap(
  basemap: BasemapType
): basemap is VideoBasemapType {
  return basemap.type === 'video_extend' || basemap.type === 'video_first_frame'
}

export function isImageBasemap(
  basemap: BasemapType
): basemap is ImageBasemapType {
  return (
    basemap.type === 'sref' ||
    basemap.type === 'cref' ||
    basemap.type === 'content' ||
    basemap.type === 'remix'
  )
}

type State = {
  prompt: string
  aspectRatio: string
  basemap: BasemapType[]
  jobList: JobItemType[]

  searchQuery: string
  searchResults: VCGImageType[]
  searchLoading: boolean
  searchError: string
  promptLoading: boolean
  generationLoading: boolean
  jobTotal: number
  jobLoading: boolean
  serverPullJobTotal: number
  generationType: GenerationType
}

type Actions = {
  setPrompt: (prompt: string) => void
  addJob: (job: JobItemType) => void
  updateJob: (id: string, job: UpdateJobItemType) => void
  generate: (callback?: () => void) => void
  variation: (
    jobId: string,
    imageNo: number,
    type: number,
    callback?: () => void
  ) => void
  upscale: (
    jobId: string,
    imageNo: number,
    type: number,
    callback?: () => void
  ) => void
  remix: (
    jobId: string,
    imageNo: number,
    type: number,
    callback?: () => void
  ) => void
  pan: (
    jobId: string,
    imageNo: number,
    direction: number,
    scale?: number,
    callback?: () => void
  ) => void
  outpaint: (
    jobId: string,
    imageNo: number,
    scale: number,
    callback?: () => void
  ) => void
  removeBackground: (
    imageUrl: string,
    jobId?: string,
    callback?: () => void
  ) => void
  getLocalJob: (id: string) => JobItemType | undefined
  getJobInfo: (id: string) => Promise<ResponseType<DiffusionDataType>>
  setAspectRatio: (aspectRatio: string) => void
  addBasemap: (basemap: BasemapType[]) => void
  setBasemap: (basemap: BasemapType[]) => void
  removeBasemap: (url: string, type: BasemapImageClassType | 'remix') => void
  clearBasemap: () => void
  updateBasemapType: (
    url: string,
    type: BasemapImageClassType,
    newType: BasemapImageClassType
  ) => void
  jobReroll: (id: string, callback?: () => void) => void
  setImageRemixBasemap: (jobId: string, imageNo: number, mode: number) => void
  clearImageRemixBasemap: () => void

  setSearchQuery: (searchQuery: string) => void
  setSearchResults: (searchResults: VCGImageType[]) => void
  setSearchLoading: (searchLoading: boolean) => void
  searchImageFn: () => void
  optimizationPromptFn: () => void
  getJobHistoryFn: (id?: number, pageSize?: number) => void
  setGenerationType: (generationType: GenerationType) => void
  autoExtendVideo: (
    jobId: string,
    videoNo: number,
    motion?: motionType,
    callback?: () => void
  ) => void
  videoUpscale: (jobId: string, videoNo: number, callback?: () => void) => void
  clearVideoBasemap: () => void
  setVideoFirstFrame: (parameter: VideoGenerationParameter) => void
  setVideoExtend: (parameter: VideoExtendBasemapType) => void
  setArticle: (article: ArticleGenerationBasemapType) => void
  addImageBasemap: (basemap: ImageBasemapType[]) => void
  updateImageBasemapType: (
    url: string,
    type: BasemapImageClassType,
    newType: BasemapImageClassType
  ) => void
}
export const useCreationStore = create<State & Actions>((set, get) => ({
  prompt: '',
  jobList: [],
  aspectRatio: DEFAULT_ASPECT_RATIO,
  basemap: [],
  setImageRemixBasemap: (jobId, imageNo, mode) => {
    const { jobList } = get()

    if (!jobList.length) {
      toast.error('重塑图片不存在')
      return
    }
    const job = jobList.find(item => item.id === jobId)

    if (!job) {
      toast.error('重塑图片不存在')
      return
    }
    const { urls = [], is_copyright = 0, text = '', basemap = [] } = job
    const { data } = PromptCommandParser.parse(text)
    const newBasemap = unique(
      basemap.concat([
        {
          type: 'remix',
          jobId,
          imageNo,
          mode,
          url: urls[imageNo]?.url,
          is_copyright,
        },
      ]),
      item => item.type
    )
    set({
      basemap: newBasemap,
      prompt: data?.prompt || text,
      generationType: 'image',
    })
    toast.info('已设置重塑对象，请点击“重塑图片”按钮进行重塑')
  },
  clearImageRemixBasemap: () => {
    const { basemap } = get()
    const newBasemap = basemap.filter(item => item.type !== 'remix')
    set({ basemap: newBasemap })
  },
  setPrompt: prompt => {
    const { generationType, setArticle } = get()
    set({ prompt })

    if (generationType === 'article') {
      setArticle({ prompt })
    }
  },
  setAspectRatio: aspectRatio => set({ aspectRatio }),
  getLocalJob: id => {
    const { jobList } = get()
    const localeJob = jobList.find(item => item.id === id)

    return localeJob
  },
  addBasemap: (es = []) => {
    const { basemap, generationType, searchQuery, prompt } = get()

    if (generationType !== 'image') {
      set({
        generationType: 'image',
        basemap: [],
      })
    }
    set({
      basemap: unique(
        basemap.concat(es),
        item => `${'url' in item ? item.url : ''}-${item.type}`
      ),
    })

    if (!prompt.trim()) {
      set({ prompt: searchQuery.trim() })
    }
  },
  addImageBasemap: (es = []) => {
    const { basemap, generationType, searchQuery, prompt } = get()

    if (generationType !== 'image') {
      set({
        generationType: 'image',
        basemap: [],
      })
    }
    set({
      basemap: unique(
        basemap.concat(es),
        item => `${'url' in item ? item.url : ''}-${item.type}`
      ),
    })

    if (!prompt.trim()) {
      set({ prompt: searchQuery.trim() })
    }
  },
  setBasemap: basemap => set({ basemap }),
  clearBasemap: () => set({ basemap: [] }),
  removeBasemap: (url, type) => {
    const { basemap } = get()
    const newBasemap = basemap.filter(
      item => !('url' in item && item.url === url && item.type === type)
    )
    set({ basemap: newBasemap })
  },
  updateBasemapType: (url, type, newType) => {
    const { basemap } = get()
    const newBasemap = unique(
      basemap.map(item =>
        'url' in item && item.url === url && item.type === type
          ? { ...item, type: newType }
          : item
      ),
      item => `${'url' in item ? item.url : ''}-${item.type}`
    )
    set({ basemap: newBasemap })
  },
  updateImageBasemapType: (url, type, newType) => {
    const { basemap } = get()
    const newBasemap = unique(
      basemap.map(item =>
        'url' in item && item.url === url && item.type === type
          ? { ...item, type: newType }
          : item
      ),
      item => `${'url' in item ? item.url : ''}-${item.type}`
    )
    set({ basemap: newBasemap })
  },
  addJob: job => {
    const { jobList } = get()
    set({ jobList: [...jobList, job] })
    toast.info(`开始生成...`)
    creationScrollToBottom()
    // 此处使用 react-router 逻辑导航到 /creation
    // navigator(`/creation`);
  },
  generate: async (callback = () => {}) => {
    const {
      prompt,
      // aspectRatio,
      basemap = [],
      addJob,
      getLocalJob,
      searchQuery,
      generationType,
    } = get()
    const is_copyright =
      basemap.length &&
      basemap.every(item => 'is_copyright' in item && item.is_copyright === 1)
        ? 1
        : 0

    const handleImageGeneration = async () => {
      if (!prompt.trim()) {
        toast.error('请输入提示词')
        return
      }
      const p = prompt
      /**
       * url(内容 多个图片链接 空格分开) <prompt 很多字符串文字> --fast --ar 77:58(图像比例 数字 宽：高) --sref url(风格 多个图片链接 空格分开) --cref url(人脸 多个图片链接 空格分开) --v 6.1(模型版本)
       **/
      const textArr: string[] = []
      // 一次遍历收集所有类型的 URL，避免多次过滤
      const urlGroups = basemap.reduce(
        (acc, item) => {
          if (item.type === 'sref') {
            acc.sref.add(item.url)
          } else if (item.type === 'cref') {
            acc.cref.add(item.url)
          } else if (item.type === 'content' || item.type === 'remix') {
            acc.content.add(item.url)
          }
          return acc
        },
        {
          sref: new Set<string>(),
          cref: new Set<string>(),
          content: new Set<string>(),
        }
      )

      const srefUrls = Array.from(urlGroups.sref).join(' ')
      const crefUrls = Array.from(urlGroups.cref).join(' ')
      const contentUrls = Array.from(urlGroups.content).join(' ')

      if (contentUrls) {
        textArr.push(contentUrls)
      }

      // 如果 prompt 中没有 --v 7，并且 crefUrls 无数据，则 textArr 中添加 --v 7
      // 如果 prompt 中有 --v 7，并且 crefUrls 有数据，则 textArr 中不添加 --v 7
      // if (crefUrls && prompt.includes('--v 7')) {
      //   p = prompt.replace('--v 7', '')
      // }
      textArr.push(p)

      // 如果 prompt 中没有 --fast，则添加 --fast
      // if (!prompt.includes('--fast')) {
      //   textArr.push(`--fast`);
      //   jobTextArr.push(`--fast`);
      // }
      // let ar = aspectRatio
      // // 如果 prompt 中有 --ar，取出它的值，它的值类似于 --ar 1:1
      // const arMatch = prompt.match(/--ar\s+(\d+:\d+)/)
      // // console.info('arMatch', arMatch);
      // if (arMatch) {
      //   ar = arMatch[1]
      // }

      // if (!prompt.includes('--ar ')) {
      //   textArr.push(`--ar ${ar}`)
      // }

      if (srefUrls && !p.includes('--sref ')) {
        textArr.push(`--sref ${srefUrls}`)
      }

      if (crefUrls && !p.includes('--cref ')) {
        textArr.push(`--cref ${crefUrls}`)
      }

      // if (!p.includes('--v ') && !crefUrls) {
      //   textArr.push(`--v 7`)
      // }

      // if (!p.includes('--turbo')) {
      //   textArr.push(`--turbo`)
      // }

      const text = textArr.join(' ')
      const remixBasemap = basemap.find(item => item.type === 'remix')

      if (remixBasemap) {
        const { jobId, imageNo, mode } = remixBasemap
        try {
          const res = await webapi
            .post('creations/v1/remix', {
              json: {
                jobId,
                imageNo,
                mode,
                remixPrompt: text,
                is_copyright,
                basemap,
              },
            })
            .json<ResponseType<DiffusionDataType>>()

          if (res.status_code === 1) {
            const { id, seed, comment, incr_id } = res.data || {}
            const currentJob = getLocalJob(jobId)
            addJob({
              id,
              incr_id,
              type: JobType.REMIX,
              status: DiffusionStatus.PROCESSING,
              created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              seed,
              comment,
              text: text || currentJob?.text || '',
              is_copyright,
              basemap,
            })
            callback()
          } else {
            toast.error(res.message)
          }
        } catch (error) {
          console.error(error)
          toast.error('重塑接口调用失败，请稍后重试')
        }
      } else {
        try {
          const res = await webapi
            .post('creations/v1/diffusion', {
              json: {
                text,
                phrase: searchQuery.trim(),
                is_copyright,
                basemap,
              },
            })
            .json<ResponseType<DiffusionDataType>>()

          if (res.status_code === 1) {
            const { id, seed, comment, incr_id } = res.data || {}
            addJob({
              id,
              incr_id,
              text,
              type: JobType.DEFAULT,
              status: DiffusionStatus.PROCESSING,
              created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              seed,
              comment,
              is_copyright,
              basemap,
            })
            callback()
          } else {
            toast.error(res.message)
          }
        } catch (error) {
          console.error(error)
          toast.error('图片生成接口调用失败，请稍后重试')
        }
      }
    }

    const handleVideoGeneration = async () => {
      const videoBasemap = basemap.find(
        item =>
          item.type === 'video_extend' || item.type === 'video_first_frame'
      )

      if (!videoBasemap) {
        toast.error('请设置视频首帧图片')
        return
      }

      if (videoBasemap.type === 'video_extend') {
        const { jobId, videoNo, motion } = videoBasemap
        try {
          const res = await webapi
            .post('creations/v1/extend_video', {
              json: {
                jobId,
                videoNo,
                prompt,
                is_copyright,
                basemap,
              },
            })
            .json<ResponseType<DiffusionDataType>>()

          if (res.status_code === 1) {
            const { id, seed, comment, incr_id } = res.data || {}
            addJob({
              id,
              incr_id,
              type: JobType.VIDEO_EXTEND,
              status: DiffusionStatus.PROCESSING,
              created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              seed,
              comment,
              text: prompt,
              typeValue: motion,
              is_copyright,
              basemap,
            })
            callback()
          } else {
            toast.error(res.message)
          }
        } catch (error) {
          console.error(error)
          toast.error('视频延长接口调用失败，请稍后重试')
        }
      }

      if (videoBasemap.type === 'video_first_frame') {
        const { width, height, url } = videoBasemap
        const promptArr: string[] = []

        if (url) {
          promptArr.push(url)
        } else {
          toast.error('请设置视频首帧')
          return
        }
        promptArr.push(prompt)

        if (width && height) {
          promptArr.push(`--ar ${calculateRatio(width || 1, height || 1)}`)
        }
        const text = promptArr.join(' ')
        try {
          const res = await webapi
            .post('creations/v1/video_diffusion', {
              json: {
                ...shake(
                  {
                    prompt: text,
                    jobId: '',
                    imageNo: 0,
                  },
                  a => !a
                ),
                is_copyright,
                basemap,
              },
            })
            .json<ResponseType<DiffusionDataType>>()

          if (res.status_code === 1) {
            const { id, seed, comment, incr_id } = res.data || {}
            addJob({
              id,
              incr_id,
              type: JobType.VIDEO_GENERATION,
              status: DiffusionStatus.PROCESSING,
              created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
              seed,
              comment,
              text,
              is_copyright,
              basemap,
            })
            callback()
          } else {
            toast.error(res.message)
          }
        } catch (error) {
          console.error(error)
          toast.error('视频生成接口调用失败，请稍后重试')
        }
      }
    }

    const handleArticleGeneration = async () => {
      if (!prompt.trim()) {
        toast.error('请输入提示词')
        return
      }
      try {
        const article = basemap.find(isArticleBasemap) || {
          type: 'article',
          ...ARTICLE_DEFAULT_DATA,
        }
        const data = `文章类型:${article.class}\n文章长度:${article.lenght}\n语气:${article.tone}\n补充要求:${prompt}`
        const res = await webapi
          .post('creations/v1/article', {
            json: {
              data,
              text: prompt,
              is_copyright,
              basemap,
            },
          })
          .json<ResponseType<DiffusionDataType>>()

        if (res.status_code === 1) {
          const { id, seed, comment, incr_id } = res.data || {}
          addJob({
            id,
            incr_id,
            type: JobType.ARTICLE_GENERATION,
            status: DiffusionStatus.PROCESSING,
            created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            seed,
            comment,
            text: prompt,
            is_copyright,
            basemap,
          })
          callback()
        } else {
          toast.error(res.message)
        }
      } catch (error) {
        console.error(error)
        toast.error('文章生成接口调用失败，请稍后重试')
      }
    }

    switch (generationType) {
      case 'video':
        handleVideoGeneration()
        break
      case 'article':
        handleArticleGeneration()
        break
      default:
        handleImageGeneration()
        break
    }
    creationScrollToBottom()
  },
  variation: async (jobId, imageNo, type, callback = () => {}) => {
    const { getLocalJob, addJob } = get()
    const currentJob = getLocalJob(jobId)
    const { is_copyright = 0, basemap = [], text = '' } = currentJob || {}
    const res = await webapi
      .post('creations/v1/variation', {
        json: {
          jobId,
          imageNo,
          type,
          is_copyright,
          basemap,
        },
      })
      .json<ResponseType<DiffusionDataType>>()

    if (res.status_code === 1) {
      const { id, seed, comment, incr_id } = res.data || {}
      addJob({
        id,
        incr_id,
        type: JobType.VARIATION,
        status: DiffusionStatus.PROCESSING,
        created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        seed,
        comment,
        text,
        is_copyright,
        basemap,
      })
      callback()
    } else {
      toast.error(res.message)
    }
  },
  upscale: async (jobId, imageNo, type, callback = () => {}) => {
    const { getLocalJob, addJob } = get()
    const currentJob = getLocalJob(jobId)
    const { is_copyright = 0, basemap = [], text = '' } = currentJob || {}
    const res = await webapi
      .post('creations/v1/upscale', {
        json: {
          jobId,
          imageNo,
          type,
          is_copyright,
          basemap,
        },
      })
      .json<ResponseType<DiffusionDataType>>()

    if (res.status_code === 1) {
      const { id, seed, comment, incr_id } = res.data || {}
      addJob({
        id,
        incr_id,
        type: JobType.UPSCALE,
        status: DiffusionStatus.PROCESSING,
        created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        seed,
        comment,
        text,
        is_copyright,
        basemap,
      })
      callback()
    } else {
      toast.error(res.message)
    }
  },
  remix: async (jobId, imageNo, type, callback = () => {}) => {
    const { getLocalJob, addJob } = get()
    const currentJob = getLocalJob(jobId)
    const { is_copyright = 0, basemap = [], text = '' } = currentJob || {}
    const res = await webapi
      .post('creations/v1/remix', {
        json: {
          jobId,
          imageNo,
          type,
          is_copyright,
          basemap,
        },
      })
      .json<ResponseType<DiffusionDataType>>()

    if (res.status_code === 1) {
      const { id, seed, comment, incr_id } = res.data || {}
      addJob({
        id,
        incr_id,
        type: JobType.REMIX,
        status: DiffusionStatus.PROCESSING,
        created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        seed,
        comment,
        text,
        is_copyright,
        basemap,
      })
      callback()
    } else {
      toast.error(res.message)
    }
  },
  pan: async (jobId, imageNo, direction, scale = 1.2, callback = () => {}) => {
    const { getLocalJob, addJob } = get()
    const currentJob = getLocalJob(jobId)
    const { is_copyright = 0, basemap = [], text = '' } = currentJob || {}
    const res = await webapi
      .post('creations/v1/pan', {
        json: {
          jobId,
          imageNo,
          direction,
          scale,
          is_copyright,
          basemap,
        },
      })
      .json<ResponseType<DiffusionDataType>>()

    if (res.status_code === 1) {
      const { id, seed, comment, incr_id } = res.data || {}
      addJob({
        id,
        incr_id,
        type: JobType.PAN,
        status: DiffusionStatus.PROCESSING,
        created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        seed,
        comment,
        text,
        is_copyright,
        basemap,
      })
      callback()
    } else {
      toast.error(res.message)
    }
  },
  outpaint: async (jobId, imageNo, scale = 1.5, callback = () => {}) => {
    const { getLocalJob, addJob } = get()
    const currentJob = getLocalJob(jobId)
    const { is_copyright = 0, basemap = [], text = '' } = currentJob || {}
    const res = await webapi
      .post('creations/v1/outpaint', {
        json: {
          jobId,
          imageNo,
          scale,
          is_copyright,
          basemap,
        },
      })
      .json<ResponseType<DiffusionDataType>>()

    if (res.status_code === 1) {
      const { id, seed, comment, incr_id } = res.data || {}
      addJob({
        id,
        incr_id,
        type: JobType.OUTPAINT,
        status: DiffusionStatus.PROCESSING,
        created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        seed,
        comment,
        text,
        is_copyright,
        basemap,
      })
      callback()
    } else {
      toast.error(res.message)
    }
  },
  removeBackground: async (imageUrl, jobId, callback = () => {}) => {
    const { getLocalJob, addJob } = get()
    const res = await webapi
      .post('creations/v1/remove_background', {
        json: {
          imgUrl: imageUrl,
        },
      })
      .json<ResponseType<DiffusionDataType>>()

    if (res.status_code === 1) {
      const { id, seed, comment, incr_id } = res.data || {}
      const currentJob = getLocalJob(jobId || '')
      addJob({
        id,
        incr_id,
        type: JobType.REMOVE_BACKGROUND,
        status: DiffusionStatus.PROCESSING,
        created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        seed,
        comment,
        text: currentJob?.text || '',
      })
      callback()
    } else {
      toast.error(res.message)
    }
  },

  getJobInfo: async id => {
    const res = await webapi
      .get('creations/v1/job', {
        searchParams: {
          jobId: id,
        },
      })
      .json<ResponseType<DiffusionDataType>>()

    return res
  },
  updateJob: (id, job) => {
    const { jobList } = get()
    set({
      jobList: jobList.map(item =>
        item.id === id ? { ...item, ...job } : item
      ),
    })
  },
  jobReroll: async (jobId, callback = () => {}) => {
    const { addJob, getLocalJob } = get()
    const currentJob = getLocalJob(jobId)
    const { is_copyright = 0, basemap = [], type } = currentJob || {}
    const res = await webapi
      .post('creations/v1/reroll', {
        json: {
          jobId,
          is_copyright,
          basemap,
        },
      })
      .json<ResponseType<DiffusionDataType>>()

    if (res.status_code === 1) {
      const { id: newId, incr_id, text, seed, comment } = res.data || {}

      if (!currentJob) {
        toast.error('任务不存在')
        return
      }
      addJob({
        id: newId,
        incr_id,
        text,
        type: type || JobType.DEFAULT,
        status: DiffusionStatus.PROCESSING,
        created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        seed,
        comment,
        is_copyright,
        basemap,
      })
      callback()
    } else {
      toast.error(res.message)
    }
  },
  searchQuery: '',
  searchError: '',
  searchResults: [],
  searchLoading: false,
  promptLoading: false,
  setSearchQuery: searchQuery => set({ searchQuery }),
  setSearchResults: searchResults => set({ searchResults }),
  setSearchLoading: searchLoading => set({ searchLoading }),
  searchImageFn: async () => {
    const { searchLoading, searchQuery } = get()

    if (searchLoading) {
      return
    }

    const query = searchQuery.trim()

    if (!query) {
      toast.error('请输入搜索内容')
      return
    }
    set({
      searchLoading: true,
      searchError: '',
      searchResults: [],
    })
    try {
      const imagesRes = await webapi
        .post('search/image', {
          json: {
            text: query,
          },
        })
        .json<ResponseType<VCGImageDataItemType[]>>()
      const { status_code = 0, data = [] } = imagesRes

      if (status_code !== 1) {
        set({
          // searchError: message || '图库获取图片信息接口调用失败，请稍后重试',
          searchError: '抱歉，没有找到相关的内容',
          searchLoading: false,
          searchResults: [],
        })
        return
      }
      const images = data.map(image => {
        const { id, title, oss800, picWidth, picHeight } = image

        return {
          id,
          title,
          url: oss800,
          width: picWidth,
          height: picHeight,
        }
      })
      set({
        searchResults: images,
        searchLoading: false,
        searchError: '',
      })
    } catch (error) {
      console.error(error)
      set({
        searchResults: [],
        searchLoading: false,
        // searchError: '图库搜索接口调用失败，请稍后重试',
        searchError: '抱歉，没有找到相关的内容',
      })
    }
  },
  optimizationPromptFn: async () => {
    const { prompt, setPrompt, promptLoading } = get()

    if (promptLoading) {
      toast.info('正在优化提示词，请稍后...')
      return
    }
    const query = prompt.trim()

    if (!query) {
      toast.error('请输入需要生成的内容')
      return
    }

    set({ promptLoading: true })
    try {
      const res = await webapi
        .post('prompt', {
          json: {
            data: query,
          },
        })
        .json<ResponseType<string>>()

      if (res.status_code === 1) {
        setPrompt(res.data)
      } else {
        toast.error(res.message)
      }
    } catch (error) {
      console.error(error)
      toast.error('优化提示词接口调用失败，请稍后重试')
    } finally {
      set({ promptLoading: false })
    }
  },
  generationLoading: false,
  jobTotal: 0,
  serverPullJobTotal: 0,
  jobLoading: false,
  getJobHistoryFn: async (id, pageSize = 10) => {
    const { jobLoading, serverPullJobTotal = 0 } = get()

    if (jobLoading) {
      return
    }
    set({ jobLoading: true })
    const toastId = toast.loading('正在获取任务历史...')
    try {
      const res = await webapi
        .get('my/jobs', {
          searchParams: shake({
            offset_id: id,
            page_size: pageSize,
          }),
        })
        .json<
          ResponseType<{
            list: JobItemType[]
            total: number
          }>
        >()

      if (res.status_code === 1) {
        const { jobList: oldJobList } = get()
        const { list, total = 0 } = res.data || {}
        const len = list.length || 0

        if (len) {
          set({
            jobList: [...(list.reverse() || []), ...oldJobList],
            jobTotal: total,
            serverPullJobTotal: serverPullJobTotal + len,
          })
        } else {
          toast.info('没有更多任务了')
        }
      } else {
        toast.error(res.message)
      }
    } catch (error) {
      console.error(error)
      toast.error('获取任务历史记录失败，请重试')
    } finally {
      set({ jobLoading: false })

      if (toastId) {
        setTimeout(() => {
          toast.dismiss(toastId)
        }, 300)
      }
    }
  },
  generationType: 'image',
  setGenerationType: generationType => {
    set({
      generationType,
      basemap:
        generationType === 'article'
          ? [
              {
                type: 'article',
                ...ARTICLE_DEFAULT_DATA,
              },
            ]
          : [],
    })
  },
  autoExtendVideo: async (jobId, videoNo, motion, callback = () => {}) => {
    const { getLocalJob, addJob } = get()
    const currentJob = getLocalJob(jobId)
    if (!currentJob) {
      toast.error('任务不存在')
      return
    }
    const { text, is_copyright = 0, basemap = [] } = currentJob
    const { data } = PromptCommandParser.parse(text)
    const prompt = (data?.prompt || '') + (motion ? ` --motion ${motion}` : '')
    try {
      const res = await webapi
        .post('creations/v1/extend_video', {
          json: {
            jobId,
            videoNo,
            prompt,
            is_copyright,
            basemap,
          },
        })
        .json<ResponseType<DiffusionDataType>>()

      if (res.status_code === 1) {
        const { id, seed, comment, incr_id } = res.data || {}
        addJob({
          id,
          incr_id,
          type: JobType.VIDEO_EXTEND,
          status: DiffusionStatus.PROCESSING,
          created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          seed,
          comment,
          text: prompt,
          typeValue: motion,
          is_copyright,
          basemap,
        })
        callback()
      } else {
        toast.error(res.message)
      }
    } catch (error) {
      console.error(error)
      toast.error('视频延长接口调用失败，请稍后重试')
    }
  },
  videoUpscale: async (jobId, videoNo, callback = () => {}) => {
    const { getLocalJob, addJob } = get()
    const currentJob = getLocalJob(jobId)
    const { is_copyright = 0, basemap = [], text = '' } = currentJob || {}
    const res = await webapi
      .post('creations/v1/video_upscale', {
        json: {
          jobId,
          videoNo,
          is_copyright,
          basemap,
        },
      })
      .json<ResponseType<DiffusionDataType>>()

    if (res.status_code === 1) {
      const { id, seed, comment, incr_id } = res.data || {}
      addJob({
        id,
        incr_id,
        type: JobType.VIDEO_UPSCALE,
        status: DiffusionStatus.PROCESSING,
        created_at: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        seed,
        comment,
        text,
        is_copyright,
        basemap,
      })
      callback()
    } else {
      toast.error(res.message)
    }
  },
  clearVideoBasemap: () => {
    const { basemap } = get()

    if (basemap) {
      const newBasemap =
        basemap.filter(
          item =>
            item.type !== 'video_extend' && item.type !== 'video_first_frame'
        ) || []
      set({ basemap: newBasemap })
    }
  },
  setVideoFirstFrame: parameter => {
    set({
      basemap: [parameter],
      generationType: 'video',
    })
  },
  setVideoExtend(parameter) {
    set({
      basemap: [parameter],
      generationType: 'video',
    })
  },
  setArticle(newData) {
    const { basemap } = get()
    const article = basemap.find(isArticleBasemap) || {
      type: 'article',
      ...ARTICLE_DEFAULT_DATA,
    }
    set({
      generationType: 'article',
      basemap: [{ ...article, ...newData }],
    })
  },
}))
