import { Tooltip<PERSON>rovider } from '@radix-ui/react-tooltip'
import { createRoot } from 'react-dom/client'
import { createBrowserRouter, RouterProvider } from 'react-router'
import { ThemeProvider } from './components/theme-provider'
import ErrorPage from './error-page'
import accountLoader from './loader/account'
import creationDetailLoader from './loader/creation_detail.ts'
import productDetailLoader from './loader/product_detail.ts'
import AccountPage from './routes/account'
import AdvancedEdit from './routes/advanced_edit'
import AdvancedEditDetail from './routes/advanced_edit_detail'
import CreationDetail from './routes/creation_detail'
import Home from './routes/home'
import Login from './routes/login'
import Product from './routes/product'
import ProductDetail from './routes/product_detail'
import Root from './routes/root'
import './index.css'

const router = createBrowserRouter([
  {
    path: '/',
    element: <Root />,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        element: <Home />,
      },
      {
        path: '/creation/:jobid',
        element: <CreationDetail />,
        loader: creationDetailLoader,
      },
      {
        path: '/advanced-edit',
        element: <AdvancedEdit />,
      },
      {
        path: '/advanced-edit/new',
        element: <AdvancedEditDetail />,
      },
      {
        path: '/advanced-edit/:sessionid',
        element: <AdvancedEditDetail />,
      },
      {
        path: '/login',
        element: <Login />,
      },
      {
        path: '/product',
        element: <Product />,
      },
      {
        path: '/account',
        element: <AccountPage />,
        loader: accountLoader,
      },
      {
        path: '/product/:jobid',
        element: <ProductDetail />,
        loader: productDetailLoader,
      },
    ],
  },
])

const rootElement = document.getElementById('root')

if (!rootElement) throw new Error('Root element not found')
createRoot(rootElement).render(
  <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
    <TooltipProvider>
      <RouterProvider router={router} />
    </TooltipProvider>
  </ThemeProvider>
)
