import { ArrowRight, Image } from 'lucide-react'
import { NavLink } from 'react-router'
import 'dayjs/locale/zh-cn'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime' // ES 2015
import { isArray } from 'radash'
import { Fragment, useCallback, useEffect, useMemo, useState } from 'react'
import {
  type JobItemType,
  type SessionItemDataType,
  useAdvancedEditStore,
} from '@/stores/advanced-edit'

dayjs.locale('zh-cn')
dayjs.extend(relativeTime)

// const tags = [
//   {
//     id: 'hidden',
//     label: '隐藏',
//   },
//   {
//     id: 'multi_room',
//     label: '多人房间',
//   },
//   {
//     id: 'my_collection',
//     label: '我的精选投稿',
//   },
// ] as const
// const classes = [
//   {
//     id: 'image',
//     label: '图片',
//   },
//   {
//     id: 'video',
//     label: '视频',
//   },
//   {
//     id: 'natural',
//     label: '原始生图',
//   },
//   {
//     id: 'high_quality',
//     label: '高清生图',
//   },
// ] as const

// // 图像比例
// const image_ratios = [
//   {
//     id: 'square',
//     label: '方图',
//   },
//   {
//     id: 'portrait',
//     label: '纵向',
//   },
//   {
//     id: 'landscape',
//     label: '横向',
//   },
// ] as const

// // 版本
// const versions = [
//   {
//     id: '7',
//     label: '7',
//   },
//   {
//     id: '6.1',
//     label: '6.1',
//   },
//   {
//     id: 'niji6',
//     label: 'Niji 6',
//   },
//   {
//     id: '6',
//     label: '6',
//   },
//   {
//     id: '5',
//     label: '5',
//   },
// ] as const

// // 其他
// const others = [
//   {
//     id: 'tile',
//     label: '平铺模式',
//   },
//   {
//     id: 'original',
//     label: '原始模式',
//   },
// ] as const

// // 展示布局
// const layouts = [
//   {
//     id: 'square',
//     label: '正方形',
//   },
//   {
//     id: 'original_ratio',
//     label: '原始比例',
//   },
// ] as const

// // 图片尺寸
// const image_sizes = [
//   {
//     id: 'small',
//     label: '小',
//   },
//   {
//     id: 'medium',
//     label: '中',
//   },
//   {
//     id: 'large',
//     label: '大',
//   },
// ] as const

// const FormSchema = z.object({
//   tags: z.array(z.string()).refine(value => value.some(item => item), {
//     message: 'You have to select at least one item.',
//   }),
//   classes: z.array(z.string()).refine(value => value.some(item => item), {
//     message: 'You have to select at least one item.',
//   }),
//   image_ratios: z.array(z.string()).refine(value => value.some(item => item), {
//     message: 'You have to select at least one item.',
//   }),
//   versions: z.array(z.string()).refine(value => value.some(item => item), {
//     message: 'You have to select at least one item.',
//   }),
//   others: z.array(z.string()).refine(value => value.some(item => item), {
//     message: 'You have to select at least one item.',
//   }),
//   layouts: z.enum(['square', 'original_ratio']).optional(),
//   image_sizes: z.enum(['small', 'medium', 'large']).optional(),
// })

export default function AdvancedEdit() {
  const { getSessions, getAllJobs } = useAdvancedEditStore()
  const [sessions, setSessions] = useState<SessionItemDataType[]>([])
  const [jobs, setJobs] = useState<JobItemType[]>([])
  // const form = useForm<z.infer<typeof FormSchema>>({
  //   resolver: zodResolver(FormSchema),
  //   defaultValues: {
  //     tags: [],
  //     classes: [],
  //     image_ratios: [],
  //     versions: [],
  //     others: [],
  //     layouts: undefined,
  //     image_sizes: undefined,
  //   },
  // })
  // function onSubmit(data: z.infer<typeof FormSchema>) {
  //   toast('You submitted the following values', {
  //     description: (
  //       <pre className="mt-2 w-[320px] rounded-md bg-neutral-950 p-4">
  //         <code className="text-white">{JSON.stringify(data, null, 2)}</code>
  //       </pre>
  //     ),
  //   })
  // }
  const handleData = useCallback(async () => {
    // const { data: sessionsData, status_code: sessionsStatusCode } =
    //   await getSessions(1, 20)

    //   const { data: jobsData, status_code: jobsStatusCode } = await getAllJobs()
    const [sessionsPromise, jobsPromise] = await Promise.allSettled([
      getSessions(1, 20),
      getAllJobs(),
    ])

    if (
      sessionsPromise.status === 'fulfilled' &&
      sessionsPromise.value.status_code === 1
    ) {
      setSessions(sessionsPromise.value.data?.list || [])
    }
    if (
      jobsPromise.status === 'fulfilled' &&
      jobsPromise.value.status_code === 1
    ) {
      setJobs(jobsPromise.value.data.list)
    }
  }, [getSessions, getAllJobs])
  const jobsDataList = useMemo(() => {
    return jobs.reduce(
      (acc, job) => {
        const date = dayjs(job.created_at).format('YYYY-MM-DD')
        const index = acc.findIndex(item => item.date === date)

        if (index === -1) {
          acc.push({ date, jobs: [job] })
        } else {
          acc[index].jobs.push(job)
        }

        return acc
      },
      [] as { date: string; jobs: JobItemType[] }[]
    )
  }, [jobs])
  useEffect(() => {
    handleData()
  }, [handleData])

  return (
    <div className="relative p-4 overflow-y-auto bg-muted/40">
      <div className="pb-4 pt-10">
        <NavLink
          to="/advanced-edit/new"
          className="flex w-80 h-36 justify-between items-end gap-2 p-4 rounded-lg border"
          viewTransition
        >
          <div className="flex items-center gap-2">
            <Image className="size-5" />
            <p className="text-base">新图片编辑</p>
          </div>
          <ArrowRight />
        </NavLink>
      </div>
      <div className="py-4 space-y-4">
        <p>编辑记录</p>
        <div className="overflow-x-auto">
          <div className="w-full flex gap-[1px] snap-x scroll-pl-0 overflow-x-auto pb-4">
            {sessions.length ? (
              sessions.map(({ session_id, url, text, thumbnail }) => (
                <NavLink
                  key={session_id}
                  to={`/advanced-edit/${session_id}`}
                  className="snap-start shrink-0 first:pl-0 relative flex group items-center justify-center cursor-pointer size-44 first:rounded-l-lg last:rounded-r-lg text-muted-foreground overflow-hidden"
                >
                  <img
                    src={thumbnail || url}
                    alt="session"
                    className="size-full object-cover"
                  />
                  {text && (
                    <p className="absolute bottom-0 left-0 right-0 text-center text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-ellipsis overflow-hidden whitespace-nowrap bg-black/30 px-2 py-1 text-white">
                      {text}
                    </p>
                  )}
                </NavLink>
              ))
            ) : (
              <div className="flex items-center justify-center select-none size-44 rounded-lg border border-dashed text-muted-foreground">
                暂无历史记录
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="py-8 flex gap-4">
        <div className="flex-1">
          {jobsDataList.map(({ date, jobs: jobList = [] }) => (
            <div className="space-y-4 mb-10" key={date}>
              <p className="pl-2">
                {dayjs().diff(date, 'day') <= 1 ? dayjs(date).fromNow() : date}
              </p>
              <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-7 2xl:grid-cols-8 gap-[1px] rounded-xl overflow-hidden">
                {jobList.map(({ id, urls = [], session_id }) => (
                  <Fragment key={id}>
                    {isArray(urls) &&
                      urls.map(
                        ({ url, id: uploadId, no, thumbnail }, index) => (
                          <NavLink
                            to={`/advanced-edit/${session_id}?jobId=${id}&index=${no || index}`}
                            className="bg-muted relative before:content-[''] before:block before:w-full before:pt-[100%] cursor-pointer last:rounded-br-xl last:overflow-hidden group"
                            key={`${session_id}-${uploadId || index}-item-key`}
                          >
                            <img
                              src={thumbnail || url}
                              alt="job"
                              loading="lazy"
                              className="size-full object-cover z-10 absolute top-0 left-0"
                            />
                          </NavLink>
                        )
                      )}
                  </Fragment>
                ))}
              </div>
            </div>
          ))}
        </div>
        {/* <div className="w-60 relative hidden">
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="sticky top-0"
            >
              <Accordion
                type="multiple"
                className="w-full border rounded-xl"
                defaultValue={['item-1']}
              >
                <AccordionItem value="item-1" className="px-4">
                  <AccordionTrigger className="hover:no-underline">
                    图片筛选
                  </AccordionTrigger>
                  <AccordionContent className="flex flex-col gap-4 text-balance">
                    <FormField
                      control={form.control}
                      name="tags"
                      render={() => (
                        <FormItem>
                          <div className="mb-1 mt-2">
                            <FormLabel className="text-sm">标记</FormLabel>
                          </div>
                          {tags.map(item => (
                            <FormField
                              key={item.id}
                              control={form.control}
                              name="tags"
                              render={({ field }) => {
                                return (
                                  <FormItem
                                    key={item.id}
                                    className="flex flex-row items-center gap-2 space-y-0"
                                  >
                                    <FormControl>
                                      <Checkbox
                                        checked={field.value?.includes(item.id)}
                                        onCheckedChange={checked => {
                                          return checked
                                            ? field.onChange([
                                                ...field.value,
                                                item.id,
                                              ])
                                            : field.onChange(
                                                field.value?.filter(
                                                  value => value !== item.id
                                                )
                                              )
                                        }}
                                      />
                                    </FormControl>
                                    <FormLabel className="text-sm font-normal">
                                      {item.label}
                                    </FormLabel>
                                  </FormItem>
                                )
                              }}
                            />
                          ))}
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="classes"
                      render={() => (
                        <FormItem>
                          <div className="mb-1 mt-2">
                            <FormLabel className="text-sm">类型</FormLabel>
                          </div>
                          {classes.map(item => (
                            <FormField
                              key={item.id}
                              control={form.control}
                              name="classes"
                              render={({ field }) => {
                                return (
                                  <FormItem
                                    key={item.id}
                                    className="flex flex-row items-center gap-2 space-y-0"
                                  >
                                    <FormControl>
                                      <Checkbox
                                        checked={field.value?.includes(item.id)}
                                        onCheckedChange={checked => {
                                          return checked
                                            ? field.onChange([
                                                ...field.value,
                                                item.id,
                                              ])
                                            : field.onChange(
                                                field.value?.filter(
                                                  value => value !== item.id
                                                )
                                              )
                                        }}
                                      />
                                    </FormControl>
                                    <FormLabel className="text-sm font-normal">
                                      {item.label}
                                    </FormLabel>
                                  </FormItem>
                                )
                              }}
                            />
                          ))}
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="image_ratios"
                      render={() => (
                        <FormItem>
                          <div className="mb-1 mt-2">
                            <FormLabel className="text-sm">图像比例</FormLabel>
                          </div>
                          {image_ratios.map(item => (
                            <FormField
                              key={item.id}
                              control={form.control}
                              name="image_ratios"
                              render={({ field }) => {
                                return (
                                  <FormItem
                                    key={item.id}
                                    className="flex flex-row items-center gap-2 space-y-0"
                                  >
                                    <FormControl>
                                      <Checkbox
                                        checked={field.value?.includes(item.id)}
                                        onCheckedChange={checked => {
                                          return checked
                                            ? field.onChange([
                                                ...field.value,
                                                item.id,
                                              ])
                                            : field.onChange(
                                                field.value?.filter(
                                                  value => value !== item.id
                                                )
                                              )
                                        }}
                                      />
                                    </FormControl>
                                    <FormLabel className="text-sm font-normal">
                                      {item.label}
                                    </FormLabel>
                                  </FormItem>
                                )
                              }}
                            />
                          ))}
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="versions"
                      render={() => (
                        <FormItem>
                          <div className="mb-1 mt-2">
                            <FormLabel className="text-sm">版本</FormLabel>
                          </div>
                          {versions.map(item => (
                            <FormField
                              key={item.id}
                              control={form.control}
                              name="versions"
                              render={({ field }) => {
                                return (
                                  <FormItem
                                    key={item.id}
                                    className="flex flex-row items-center gap-2 space-y-0"
                                  >
                                    <FormControl>
                                      <Checkbox
                                        checked={field.value?.includes(item.id)}
                                        onCheckedChange={checked => {
                                          return checked
                                            ? field.onChange([
                                                ...field.value,
                                                item.id,
                                              ])
                                            : field.onChange(
                                                field.value?.filter(
                                                  value => value !== item.id
                                                )
                                              )
                                        }}
                                      />
                                    </FormControl>
                                    <FormLabel className="text-sm font-normal">
                                      {item.label}
                                    </FormLabel>
                                  </FormItem>
                                )
                              }}
                            />
                          ))}
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="others"
                      render={() => (
                        <FormItem>
                          <div className="mb-1 mt-2">
                            <FormLabel className="text-sm">其他</FormLabel>
                          </div>
                          {others.map(item => (
                            <FormField
                              key={item.id}
                              control={form.control}
                              name="others"
                              render={({ field }) => {
                                return (
                                  <FormItem
                                    key={item.id}
                                    className="flex flex-row items-center gap-2 space-y-0"
                                  >
                                    <FormControl>
                                      <Checkbox
                                        checked={field.value?.includes(item.id)}
                                        onCheckedChange={checked => {
                                          return checked
                                            ? field.onChange([
                                                ...field.value,
                                                item.id,
                                              ])
                                            : field.onChange(
                                                field.value?.filter(
                                                  value => value !== item.id
                                                )
                                              )
                                        }}
                                      />
                                    </FormControl>
                                    <FormLabel className="text-sm font-normal">
                                      {item.label}
                                    </FormLabel>
                                  </FormItem>
                                )
                              }}
                            />
                          ))}
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="item-2" className="px-4">
                  <AccordionTrigger className="hover:no-underline">
                    浏览选项
                  </AccordionTrigger>
                  <AccordionContent className="flex flex-col gap-4 text-balance">
                    <FormField
                      control={form.control}
                      name="layouts"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <div className="mb-1 mt-2">
                            <FormLabel className="text-sm">展示布局</FormLabel>
                          </div>
                          <FormControl>
                            <RadioGroup
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              className="flex flex-col"
                            >
                              {layouts.map(item => (
                                <FormItem
                                  key={item.id}
                                  className="flex items-center gap-2 space-y-0"
                                >
                                  <FormControl>
                                    <RadioGroupItem value={item.id} />
                                  </FormControl>
                                  <FormLabel className="font-normal">
                                    {item.label}
                                  </FormLabel>
                                </FormItem>
                              ))}
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="image_sizes"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <div className="mb-1 mt-2">
                            <FormLabel className="text-sm">图片尺寸</FormLabel>
                          </div>
                          <FormControl>
                            <RadioGroup
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              className="flex flex-col"
                            >
                              {image_sizes.map(item => (
                                <FormItem
                                  key={item.id}
                                  className="flex items-center gap-2 space-y-0"
                                >
                                  <FormControl>
                                    <RadioGroupItem value={item.id} />
                                  </FormControl>
                                  <FormLabel className="font-normal">
                                    {item.label}
                                  </FormLabel>
                                </FormItem>
                              ))}
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </form>
          </Form>
        </div> */}
      </div>
    </div>
  )
}
