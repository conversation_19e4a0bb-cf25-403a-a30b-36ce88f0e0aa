import { type ClassValue, clsx } from 'clsx'
import domtoimage from 'dom-to-image'
import saveAs from 'file-saver'
import { del, get, set } from 'idb-keyval'
import ky from 'ky'
import { toast } from 'sonner'
import { twMerge } from 'tailwind-merge'
import type { StateStorage } from 'zustand/middleware'
import { API_URL, type ImageInfoType } from '@/config'
import type { UploadDataType } from '@/stores/advanced-edit'
import { type GenerationType, JobType } from '@/stores/creation'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function handle401Redirect() {
  // 为了防止重定向循环，如果已在登录页则不执行
  if (window.location.pathname === '/login') {
    return
  }
  const currentRoute = window.location.pathname + window.location.search
  toast.error('登录已过期，请重新登录')
  window.location.replace(`/login?redirect=${encodeURIComponent(currentRoute)}`)
}

// 判断一个图片是否有透明部分
export function hasTransparentPart(file: File): Promise<boolean> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      if (!ctx) {
        resolve(false)
        return
      }

      canvas.width = img.width
      canvas.height = img.height
      ctx.drawImage(img, 0, 0)

      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
      const data = imageData.data

      for (let i = 0; i < data.length; i += 4) {
        if (data[i + 3] < 255) {
          resolve(true)
          return
        }
      }

      resolve(false)
    }

    img.onerror = () => {
      reject(new Error('Failed to load image'))
    }

    img.src = URL.createObjectURL(file)
  })
}

export const fileToBase64 = (
  file: File
): Promise<string | ArrayBuffer | null> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      resolve(reader.result)
    }
    reader.onerror = () => {
      reject(new Error('Failed to read file'))
    }
    reader.readAsDataURL(file)
  })
}

export const base64ToBlob = (
  base64: string,
  contentType = 'image/jpeg'
): Blob => {
  const byteCharacters = atob(base64)
  const byteNumbers = new Array(byteCharacters.length)
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i)
  }
  const byteArray = new Uint8Array(byteNumbers)
  return new Blob([byteArray], { type: contentType })
}

export const getToken = async (enforce: boolean = false) => {
  let token = localStorage.getItem('token')

  if (!token || enforce) {
    const res = await ky
      .get(`${API_URL}/get/token?key=014adefd106f009cb1998549b64d9cfc`)
      .json<{
        access_token: string
      }>()
    localStorage.setItem('token', res.access_token)
    token = res.access_token
  }

  return token
}

export const getLocalToken = () => localStorage.getItem('t') || ''

export const setLocalToken = (token: string) => localStorage.setItem('t', token)

export const webapi = ky.extend({
  prefixUrl: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 60000,
  retry: {
    limit: 4,
    methods: ['get', 'post', 'put', 'delete', 'patch'],
    statusCodes: [401, 403, 404, 500, 502, 503, 504],
  },
  hooks: {
    beforeRequest: [
      async request => {
        const token = getLocalToken()
        request.headers.set('Authorization', `Bearer ${token}`)
      },
    ],
    beforeRetry: [
      async ({ retryCount }) => {
        if (retryCount === 4) {
          toast.error('系统异常，请稍后重试')
        }
      },
    ],
    afterResponse: [
      async (_request, _options, response) => {
        if (response.status === 401) {
          handle401Redirect()
        }

        return response
      },
    ],
  },
})

export const uploadapi = ky.extend({
  prefixUrl: API_URL,
  timeout: 60000 * 10,
  retry: {
    limit: 2,
    methods: ['get', 'post', 'put', 'delete', 'patch'],
    statusCodes: [401, 403, 404, 500, 502, 503, 504],
  },
  hooks: {
    beforeRequest: [
      async request => {
        const token = getLocalToken()
        request.headers.set('Authorization', `Bearer ${token}`)
      },
    ],
    afterResponse: [
      async (_request, _options, response) => {
        if (response.status === 401) {
          handle401Redirect()
        }

        return response
      },
    ],
  },
})

export const blobToBase64 = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      resolve(reader.result as string)
    }
    reader.onerror = () => {
      reject(new Error('Failed to read file'))
    }
    reader.readAsDataURL(blob)
  })
}

export const urlToBase64 = async (url: string): Promise<string> => {
  const response = await fetch(url)
  const blob = await response.blob()
  return await blobToBase64(blob)
}

export const getImageInfoByUrl = async (
  url: string
): Promise<ImageInfoType> => {
  return new Promise((resolve, reject) => {
    const image = new Image()
    image.onload = async () => {
      let hasTransparentPart = false
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      if (!ctx) {
        hasTransparentPart = false
      } else {
        canvas.width = image.width
        canvas.height = image.height
        ctx.drawImage(image, 0, 0)

        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
        const data = imageData.data

        for (let i = 0; i < data.length; i += 4) {
          if (data[i + 3] < 255) {
            hasTransparentPart = true
            break
          }
        }
      }

      const base64 = await urlToBase64(url)
      console.info('base64', base64)
      resolve({
        width: image.width,
        height: image.height,
        base64: base64 as string,
        hasTransparentPart,
      })
    }

    image.onerror = () => {
      toast.error('图片加载失败，请检查图片地址是否正确')
      reject(new Error('Failed to load image'))
    }

    image.src = url
  })
}

/**
 * 使用欧几里得算法计算两个非负整数的最大公约数 (GCD)。
 * @param a 第一个数字
 * @param b 第二个数字
 * @returns a 和 b 的最大公约数
 */
export function calculateGCD(a: number, b: number): number {
  // 确保处理的是非负整数
  a = Math.abs(Math.round(a))
  b = Math.abs(Math.round(b))

  // 欧几里得算法
  while (b !== 0) {
    const temp = b
    b = a % b
    a = temp
  }
  return a
}

/**
 * 计算两个数的简化比率 (x:y)。
 * @param x 比率的第一个数
 * @param y 比率的第二个数
 * @returns 格式化为 "simplifiedX:simplifiedY" 的字符串，或在 y 为 0 时抛出错误。
 */
export function calculateRatio(x: number, y: number): string {
  if (y === 0) {
    return ''
  }

  if (x === 0) {
    return ''
  }

  // 计算最大公约数
  const commonDivisor = calculateGCD(x, y)

  // 简化比率
  const simplifiedX = x / commonDivisor
  const simplifiedY = y / commonDivisor

  // 返回格式化的字符串
  return `${simplifiedX}:${simplifiedY}`
}

export async function convertPngToBlackAndWhite(pngBlob: Blob): Promise<Blob> {
  return new Promise((resolve, reject) => {
    // 1. Blob 转 Image 对象
    const imageUrl = URL.createObjectURL(pngBlob)
    const img = new Image()

    img.onload = () => {
      // 释放临时的 Object URL
      URL.revokeObjectURL(imageUrl)

      // 2. 创建 Canvas
      const canvas = document.createElement('canvas')
      canvas.width = img.naturalWidth // 使用图片的原始尺寸
      canvas.height = img.naturalHeight
      const ctx = canvas.getContext('2d')

      if (!ctx) {
        return reject(new Error('无法获取 Canvas 2D 上下文'))
      }

      // 3. 绘制图像
      ctx.drawImage(img, 0, 0)

      try {
        // 4. 获取像素数据
        // 注意：如果图片源自其他域且未配置 CORS，这里可能会抛出安全错误
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
        const data = imageData.data // data 是 Uint8ClampedArray [R, G, B, A, R, G, B, A, ...]

        // 5. 处理像素数据
        for (let i = 0; i < data.length; i += 4) {
          const alpha = data[i + 3] // 获取 Alpha 通道值

          if (alpha === 0) {
            // 完全透明 -> 白色 (并且设为不透明)
            data[i] = 255 // Red
            data[i + 1] = 255 // Green
            data[i + 2] = 255 // Blue
            data[i + 3] = 255 // Alpha (变为不透明白色)
          } else {
            // 非完全透明 -> 黑色 (并且设为不透明)
            data[i] = 0 // Red
            data[i + 1] = 0 // Green
            data[i + 2] = 0 // Blue
            data[i + 3] = 255 // Alpha (变为不透明黑色)
          }
        }

        // 6. 写回像素数据
        ctx.putImageData(imageData, 0, 0)

        // 7. Canvas 转 Blob
        canvas.toBlob(resultBlob => {
          if (resultBlob) {
            resolve(resultBlob)
          } else {
            reject(new Error('Canvas 转换为 Blob 失败'))
          }
        }, 'image/png') // 指定输出格式为 PNG
      } catch (error) {
        reject(new Error(`处理图像数据时出错：${error}`))
      }
    }

    img.onerror = error => {
      // 释放临时的 Object URL
      URL.revokeObjectURL(imageUrl)
      reject(new Error(`加载图片失败：${error}`))
    }

    // 触发图片加载
    img.src = imageUrl
  })
}

export async function imageCompose(
  id: string,
  width: number,
  height: number,
  bgcolor: string = 'transparent'
) {
  const node = document.getElementById(id) as HTMLElement
  const base64 = await domtoimage.toPng(node, {
    quality: 1,
    width,
    height,
    bgcolor,
    style: {
      transform: `scale(1)`,
      width: `${width}px`,
      height: `${height}px`,
    },
  })
  const blob = base64ToBlob(base64.split(',')[1])
  const blackAndWhiteBlob = await convertPngToBlackAndWhite(blob)

  return {
    base64,
    blob,
    blackAndWhiteBlob,
  }
}

export async function uploadImage(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  const res = await uploadapi
    .post(`v1/upload`, {
      body: formData,
    })
    .json<{
      status_code: number
      message: string
      data: UploadDataType
    }>()
  const { status_code, message, data } = res

  if (status_code === 1) {
    return data
  } else {
    toast.error(message)
    return null
  }
}

export async function uploadImageSimple(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  const res = await uploadapi
    .post(`v1/upload/simple`, {
      body: formData,
    })
    .json<{
      status_code: number
      message: string
      data: {
        url: string
      }
    }>()
  const { status_code, message, data } = res

  if (status_code === 1) {
    return data.url
  } else {
    toast.error(message)
    return null
  }
}

export async function uploadImageByUrl(url: string) {
  const res = await webapi
    .post(`v1/upload/from/url`, {
      json: {
        url,
      },
    })
    .json<{
      status_code: number
      message: string
      data: UploadDataType
    }>()
  const { status_code, message, data } = res

  if (status_code === 1) {
    return data
  } else {
    toast.error(message)
    return null
  }
}

export const indexedDBStorage: StateStorage = {
  getItem: async (name: string): Promise<string | null> => {
    return (await get(name)) || null
  },
  setItem: async (name: string, value: string): Promise<void> => {
    await set(name, value)
  },
  removeItem: async (name: string): Promise<void> => {
    await del(name)
  },
}

// 领域模型定义 - 根据实际数据格式重新设计
interface PromptGenerationCommand {
  // 基础参数
  contentUrls: string[] // 开头的内容图片链接数组
  prompt: string // 中文描述文本
  aspectRatio: {
    // 图像比例 --ar/--aspect
    width: number
    height: number
  }
  modelVersion: string // 模型版本 --v/--version

  // 风格和参考参数
  styleUrls: string[] // 风格图片链接数组 --sref
  characterUrls: string[] // 人脸图片链接数组 --cref
  objectReferences: string[] // 万物引用图片链接数组 --oref
  styleStrength?: number // 样式强度 --s/--stylize
  objectWeights?: number[] // 万物引用权重 --ow

  // 基础控制参数
  chaos?: number // 混沌参数 --chaos/--c (0-100)
  negativePrompt?: string // 否定提示 --no
  quality?: number // 质量 --quality/--q (0.25-1.0)
  seed?: number // 种子参数 --seed
  stop?: number // 停止参数 --stop (10-100)
  rawMode?: boolean // 原始模式 --raw
  tile?: boolean // 平铺参数 --tile
  sketch?: boolean // 草图模式 --draft
  weird?: number // 怪异参数 --weird/--w (0-1000)
  imageWeight?: number // 图像权重 --iw (0-2.0)

  // 高级参数
  profile?: string // 个性化配置 --profile/--p
  repeat?: number // 重复参数 --repeat/--r
  niji?: boolean // Niji 模型 --niji
  experimental?: string // 实验参数 --exp
  fastMode?: boolean // 快速模式 --fast
  relaxMode?: boolean // 慢速模式 --relax
  turboMode?: boolean // 极速模式 --turbo

  // 视频生成参数
  isVideo?: boolean // 是否为视频生成任务
  motion?: 'low' | 'high' // 运动强度 --motion
  batchSize?: 1 | 2 | 4 // 批量大小 --bs
  endFrameUrl?: string // 尾帧图片 URL --end
  loop?: boolean // 视频循环 --loop
  videoResolution?: {
    width: number
    height: number
  }
}

// 解析结果类型
interface ParseResult {
  success: boolean
  data?: PromptGenerationCommand
  errors: string[]
}

/** biome-ignore lint/complexity/noStaticOnlyClass: This class is intentionally designed as a static utility class */
export class PromptCommandParser {
  /**
   * 解析图像生成命令字符串
   * 支持格式：[内容 URLs] [中文 prompt] [参数...]
   * 完整参数列表参考文档
   */
  static parse(commandString: string): ParseResult {
    const errors: string[] = []
    const result: Partial<PromptGenerationCommand> = {}

    try {
      // 1. 先按 -- 分割，第一部分包含内容 URLs 和 prompt
      const parts = commandString.split(/\s+--/)
      const mainPart = parts[0].trim()

      // 2. 解析主要部分：内容 URLs + prompt
      // 策略：按空格分割，URL 都以 https://开头，中文字符开始就是 prompt
      const tokens = mainPart.split(/\s+/)
      const contentUrls: string[] = []
      let promptStartIndex = 0

      // 提取开头的 URLs
      for (let i = 0; i < tokens.length; i++) {
        if (tokens[i].startsWith('https://')) {
          contentUrls.push(tokens[i])
          promptStartIndex = i + 1
        } else {
          break
        }
      }

      result.contentUrls = contentUrls

      // 提取prompt（从第一个非URL token开始到字符串结束）
      const promptTokens = tokens.slice(promptStartIndex)
      result.prompt = promptTokens.join(' ').trim()

      // 3. 初始化数组参数
      result.styleUrls = []
      result.characterUrls = []
      result.objectReferences = []
      result.objectWeights = []

      // 4. 解析各种参数
      for (let i = 1; i < parts.length; i++) {
        const part = parts[i].trim()

        // 宽高比参数 --ar/--aspect
        if (part.match(/^(ar|aspect)\s+/)) {
          const ratioMatch = part.match(/(?:ar|aspect)\s+(\d+):(\d+)/)
          if (ratioMatch) {
            result.aspectRatio = {
              width: parseInt(ratioMatch[1]),
              height: parseInt(ratioMatch[2]),
            }
          }
        }
        // 混沌参数 --chaos/--c
        else if (part.match(/^(chaos|c)\s+/)) {
          const chaosMatch = part.match(/(?:chaos|c)\s+(\d+)/)
          if (chaosMatch) {
            result.chaos = parseInt(chaosMatch[1])
          }
        }
        // 风格参考 --sref
        else if (part.startsWith('sref ')) {
          const urlsText = part.substring(5).trim()
          const urls = urlsText
            .split(/\s+/)
            .filter(url => url.startsWith('https://'))
          result.styleUrls = urls
        }
        // 角色参考 --cref
        else if (part.startsWith('cref ')) {
          const urlsText = part.substring(5).trim()
          const urls = urlsText
            .split(/\s+/)
            .filter(url => url.startsWith('https://'))
          result.characterUrls = urls
        }
        // 万物引用 --oref
        else if (part.startsWith('oref ')) {
          const urlsText = part.substring(5).trim()
          const urls = urlsText
            .split(/\s+/)
            .filter(url => url.startsWith('https://'))
          result.objectReferences = urls
        }
        // 万物引用权重 --ow
        else if (part.match(/^ow\s+/)) {
          const weightsText = part.substring(3).trim()
          const weights = weightsText
            .split(/\s+/)
            .map(w => parseFloat(w))
            .filter(w => !Number.isNaN(w))
          result.objectWeights = weights
        }
        // 样式强度 --s/--stylize
        else if (part.match(/^(s|stylize)\s+/)) {
          const strengthMatch = part.match(/(?:s|stylize)\s+(\d+)/)
          if (strengthMatch) {
            result.styleStrength = parseInt(strengthMatch[1])
          }
        }
        // 模型版本 --v/--version
        else if (part.match(/^(v|version)\s+/)) {
          const versionMatch = part.match(/(?:v|version)\s+([\d.]+)/)
          if (versionMatch) {
            result.modelVersion = versionMatch[1]
          }
        }
        // 否定提示 --no
        else if (part.startsWith('no ')) {
          result.negativePrompt = part.substring(3).trim()
        }
        // 质量 --quality/--q
        else if (part.match(/^(quality|q)\s+/)) {
          const qualityMatch = part.match(/(?:quality|q)\s+([\d.]+)/)
          if (qualityMatch) {
            result.quality = parseFloat(qualityMatch[1])
          }
        }
        // 种子 --seed
        else if (part.startsWith('seed ')) {
          const seedMatch = part.match(/seed\s+(\d+)/)
          if (seedMatch) {
            result.seed = parseInt(seedMatch[1])
          }
        }
        // 停止 --stop
        else if (part.startsWith('stop ')) {
          const stopMatch = part.match(/stop\s+(\d+)/)
          if (stopMatch) {
            result.stop = parseInt(stopMatch[1])
          }
        }
        // 怪异参数 --weird/--w
        else if (part.match(/^(weird|w)\s+/)) {
          const weirdMatch = part.match(/(?:weird|w)\s+(\d+)/)
          if (weirdMatch) {
            result.weird = parseInt(weirdMatch[1])
          }
        }
        // 图像权重 --iw
        else if (part.startsWith('iw ')) {
          const iwMatch = part.match(/iw\s+([\d.]+)/)
          if (iwMatch) {
            result.imageWeight = parseFloat(iwMatch[1])
          }
        }
        // 个性化配置 --profile/--p
        else if (part.match(/^(profile|p)\s+/)) {
          result.profile = part.substring(part.indexOf(' ') + 1).trim()
        }
        // 重复参数 --repeat/--r
        else if (part.match(/^(repeat|r)\s+/)) {
          const repeatMatch = part.match(/(?:repeat|r)\s+(\d+)/)
          if (repeatMatch) {
            result.repeat = parseInt(repeatMatch[1])
          }
        }
        // 实验参数 --exp
        else if (part.startsWith('exp ')) {
          result.experimental = part.substring(4).trim()
        }
        // 批量大小 --bs
        else if (part.startsWith('bs ')) {
          const bsMatch = part.match(/bs\s+(1|2|4)/)
          if (bsMatch) {
            result.batchSize = parseInt(bsMatch[1]) as 1 | 2 | 4
          }
        }
        // 运动强度 --motion
        else if (part.startsWith('motion ')) {
          const motionMatch = part.match(/motion\s+(low|high)/)
          if (motionMatch) {
            result.motion = motionMatch[1] as 'low' | 'high'
          }
        }
        // 尾帧图片 --end
        else if (part.startsWith('end ')) {
          const endMatch = part.match(/end\s+(https:\/\/.+)/)
          if (endMatch) {
            result.endFrameUrl = endMatch[1]
          }
        }
        // 布尔参数
        else if (part === 'raw') {
          result.rawMode = true
        } else if (part === 'tile') {
          result.tile = true
        } else if (part === 'draft') {
          result.sketch = true
        } else if (part === 'niji') {
          result.niji = true
        } else if (part === 'fast') {
          result.fastMode = true
        } else if (part === 'relax') {
          result.relaxMode = true
        } else if (part === 'turbo') {
          result.turboMode = true
        } else if (part === 'loop') {
          result.loop = true
        }
      }

      // 5. 设置默认值和验证必填项
      if (!result.aspectRatio) {
        errors.push('未找到图像比例信息 --ar/--aspect')
        result.aspectRatio = { width: 1, height: 1 }
      }

      if (!result.modelVersion) {
        errors.push('未找到模型版本 --v/--version')
        result.modelVersion = '6.1'
      }

      if (!result.prompt || result.prompt.trim().length === 0) {
        errors.push('未找到 prompt 描述文字')
        result.prompt = ''
      }

      // 6. 视频分辨率计算
      if (result.isVideo && result.aspectRatio) {
        result.videoResolution = PromptCommandParser.calculateVideoResolution(
          result.aspectRatio
        )
      }

      return {
        success: errors.length === 0,
        data: result as PromptGenerationCommand,
        errors,
      }
    } catch (error) {
      return {
        success: false,
        errors: [
          `解析过程中发生错误：${error instanceof Error ? error.message : String(error)}`,
        ],
      }
    }
  }

  /**
   * 计算视频分辨率
   */
  private static calculateVideoResolution(aspectRatio: {
    width: number
    height: number
  }): { width: number; height: number } {
    const ratio = aspectRatio.width / aspectRatio.height

    // 根据原始比例映射到视频分辨率
    const resolutionMap: { [key: string]: { width: number; height: number } } =
      {
        '1:1': { width: 624, height: 624 },
        '4:3': { width: 720, height: 544 },
        '2:3': { width: 512, height: 768 },
        '16:9': { width: 832, height: 464 },
        '1:2': { width: 448, height: 880 },
      }

    // 找到最接近的比例
    const aspectKey = `${aspectRatio.width}:${aspectRatio.height}`
    if (resolutionMap[aspectKey]) {
      return resolutionMap[aspectKey]
    }

    // 如果没有找到精确匹配，找到最接近的比例
    const availableRatios = Object.keys(resolutionMap).map(key => {
      const [w, h] = key.split(':').map(Number)
      return { key, ratio: w / h, resolution: resolutionMap[key] }
    })

    const closest = availableRatios.reduce((prev, curr) =>
      Math.abs(curr.ratio - ratio) < Math.abs(prev.ratio - ratio) ? curr : prev
    )

    return closest.resolution
  }

  /**
   * 验证解析后的数据
   */
  static validate(command: PromptGenerationCommand): string[] {
    const errors: string[] = []

    // 基础验证
    if (!command.prompt || command.prompt.trim().length === 0) {
      errors.push('prompt 不能为空')
    }

    if (command.aspectRatio.width <= 0 || command.aspectRatio.height <= 0) {
      errors.push('图像比例必须大于0')
    }

    if (command.contentUrls.length === 0) {
      errors.push('至少需要一个内容图片URL')
    }

    // 参数范围验证
    if (
      command.chaos !== undefined &&
      (command.chaos < 0 || command.chaos > 100)
    ) {
      errors.push('混沌参数 --chaos/--c 必须在 0-100 之间')
    }

    if (
      command.quality !== undefined &&
      (command.quality < 0.25 || command.quality > 1.0)
    ) {
      errors.push('质量参数 --quality/--q 必须在 0.25-1.0 之间')
    }

    if (
      command.stop !== undefined &&
      (command.stop < 10 || command.stop > 100)
    ) {
      errors.push('停止参数 --stop 必须在 10-100 之间')
    }

    if (
      command.weird !== undefined &&
      (command.weird < 0 || command.weird > 1000)
    ) {
      errors.push('怪异参数 --weird/--w 必须在 0-1000 之间')
    }

    if (
      command.imageWeight !== undefined &&
      (command.imageWeight < 0 || command.imageWeight > 2.0)
    ) {
      errors.push('图像权重 --iw 必须在 0-2.0 之间')
    }

    // 模式互斥验证
    const speedModes = [
      command.fastMode,
      command.relaxMode,
      command.turboMode,
    ].filter(Boolean).length
    if (speedModes > 1) {
      errors.push('只能选择一种速度模式：--fast, --relax, --turbo')
    }

    // 视频特定验证
    if (command.isVideo) {
      if (!command.fastMode) {
        errors.push('视频生成任务必须使用 --fast 模式')
      }

      if (
        command.batchSize !== undefined &&
        ![1, 2, 4].includes(command.batchSize)
      ) {
        errors.push('批量大小 --bs 只能是 1, 2, 或 4')
      }
    }

    // 权重和引用数量匹配验证
    if (
      command.objectWeights &&
      command.objectWeights.length > 0 &&
      command.objectWeights.length !== command.objectReferences.length
    ) {
      errors.push('万物引用权重 --ow 的数量必须与万物引用 --oref 的数量相同')
    }

    // 验证 URL 格式
    const urlPattern = /^https:\/\/.+/
    const allUrls = [
      ...command.contentUrls,
      ...command.styleUrls,
      ...command.characterUrls,
      ...command.objectReferences,
      ...(command.endFrameUrl ? [command.endFrameUrl] : []),
    ]

    allUrls.forEach(url => {
      if (!urlPattern.test(url)) {
        errors.push(`URL 格式无效：${url}`)
      }
    })

    return errors
  }

  /**
   * 格式化输出解析结果（便于调试）
   */
  static formatResult(command: PromptGenerationCommand): string {
    const sections = [
      '=== 解析结果 ===',
      `内容 URLs (${command.contentUrls.length}个):`,
      command.contentUrls.map(url => `  - ${url}`).join('\n'),
      '',
      `Prompt: ${command.prompt}`,
      '',
      `图像比例：${command.aspectRatio.width}:${command.aspectRatio.height}`,
      `模型版本：${command.modelVersion}`,
      '',
      '=== 基础控制参数 ===',
      command.chaos !== undefined ? `混沌参数: ${command.chaos}` : '',
      command.negativePrompt ? `否定提示: ${command.negativePrompt}` : '',
      command.quality !== undefined ? `质量参数: ${command.quality}` : '',
      command.seed !== undefined ? `种子参数: ${command.seed}` : '',
      command.stop !== undefined ? `停止参数: ${command.stop}` : '',
      command.rawMode ? '原始模式: 启用' : '',
      command.tile ? '平铺模式: 启用' : '',
      command.sketch ? '草图模式: 启用' : '',
      command.weird !== undefined ? `怪异参数: ${command.weird}` : '',
      command.imageWeight !== undefined
        ? `图像权重：${command.imageWeight}`
        : '',
      '',
      '=== 风格和参考参数 ===',
      command.styleStrength !== undefined
        ? `样式强度：${command.styleStrength}`
        : '',
      command.profile ? `个性化配置: ${command.profile}` : '',
      command.repeat !== undefined ? `重复参数: ${command.repeat}` : '',
      command.niji ? 'Niji 模型: 启用' : '',
      command.experimental ? `实验参数: ${command.experimental}` : '',
      '',
      command.styleUrls.length > 0
        ? `风格 URLs (${command.styleUrls.length}个):\n${command.styleUrls.map(url => `  - ${url}`).join('\n')}`
        : '',
      command.characterUrls.length > 0
        ? `人脸 URLs (${command.characterUrls.length}个):\n${command.characterUrls.map(url => `  - ${url}`).join('\n')}`
        : '',
      command.objectReferences.length > 0
        ? `万物引用 URLs (${command.objectReferences.length}个):\n${command.objectReferences.map(url => `  - ${url}`).join('\n')}`
        : '',
      command.objectWeights && command.objectWeights.length > 0
        ? `万物引用权重：${command.objectWeights.join(', ')}`
        : '',
      '',
      '=== 速度模式 ===',
      command.fastMode ? '快速模式: 启用' : '',
      command.relaxMode ? '慢速模式: 启用' : '',
      command.turboMode ? '极速模式: 启用' : '',
      '',
      '=== 视频生成参数 ===',
      command.isVideo ? '视频生成: 启用' : '',
      command.motion ? `运动强度: ${command.motion}` : '',
      command.batchSize !== undefined ? `批量大小: ${command.batchSize}` : '',
      command.endFrameUrl ? `尾帧图片: ${command.endFrameUrl}` : '',
      command.loop ? '视频循环: 启用' : '',
      command.videoResolution
        ? `视频分辨率：${command.videoResolution.width}x${command.videoResolution.height}`
        : '',
    ]

    return sections.filter(section => section.trim() !== '').join('\n')
  }

  /**
   * 构建命令字符串
   */
  static buildCommand(command: Partial<PromptGenerationCommand>): string {
    const parts: string[] = []

    // 添加内容 URLs
    if (command.contentUrls && command.contentUrls.length > 0) {
      parts.push(...command.contentUrls)
    }

    // 添加 prompt
    if (command.prompt) {
      parts.push(command.prompt)
    }

    // 添加参数
    const params: string[] = []

    // 基础参数
    if (command.aspectRatio) {
      params.push(
        `--ar ${command.aspectRatio.width}:${command.aspectRatio.height}`
      )
    }
    if (command.modelVersion) {
      params.push(`--v ${command.modelVersion}`)
    }

    // 风格和参考参数
    if (command.styleUrls && command.styleUrls.length > 0) {
      params.push(`--sref ${command.styleUrls.join(' ')}`)
    }
    if (command.characterUrls && command.characterUrls.length > 0) {
      params.push(`--cref ${command.characterUrls.join(' ')}`)
    }
    if (command.objectReferences && command.objectReferences.length > 0) {
      params.push(`--oref ${command.objectReferences.join(' ')}`)
    }
    if (command.objectWeights && command.objectWeights.length > 0) {
      params.push(`--ow ${command.objectWeights.join(' ')}`)
    }
    if (command.styleStrength !== undefined) {
      params.push(`--s ${command.styleStrength}`)
    }

    // 基础控制参数
    if (command.chaos !== undefined) {
      params.push(`--chaos ${command.chaos}`)
    }
    if (command.negativePrompt) {
      params.push(`--no ${command.negativePrompt}`)
    }
    if (command.quality !== undefined) {
      params.push(`--quality ${command.quality}`)
    }
    if (command.seed !== undefined) {
      params.push(`--seed ${command.seed}`)
    }
    if (command.stop !== undefined) {
      params.push(`--stop ${command.stop}`)
    }
    if (command.weird !== undefined) {
      params.push(`--weird ${command.weird}`)
    }
    if (command.imageWeight !== undefined) {
      params.push(`--iw ${command.imageWeight}`)
    }

    // 高级参数
    if (command.profile) {
      params.push(`--profile ${command.profile}`)
    }
    if (command.repeat !== undefined) {
      params.push(`--repeat ${command.repeat}`)
    }
    if (command.experimental) {
      params.push(`--exp ${command.experimental}`)
    }

    // 视频参数
    if (command.batchSize !== undefined) {
      params.push(`--bs ${command.batchSize}`)
    }
    if (command.motion) {
      params.push(`--motion ${command.motion}`)
    }
    if (command.endFrameUrl) {
      params.push(`--end ${command.endFrameUrl}`)
    }

    // 布尔参数
    if (command.rawMode) params.push('--raw')
    if (command.tile) params.push('--tile')
    if (command.sketch) params.push('--draft')
    if (command.niji) params.push('--niji')
    if (command.fastMode) params.push('--fast')
    if (command.relaxMode) params.push('--relax')
    if (command.turboMode) params.push('--turbo')
    if (command.loop) params.push('--loop')

    // 组合命令
    return [...parts, ...params].join(' ')
  }

  /**
   * 创建命令构建器
   */
  static builder(): PromptCommandBuilder {
    return new PromptCommandBuilder()
  }
}

/**
 * 命令构建器类
 */
class PromptCommandBuilder {
  private command: Partial<PromptGenerationCommand> = {
    contentUrls: [],
    styleUrls: [],
    characterUrls: [],
    objectReferences: [],
    objectWeights: [],
    aspectRatio: { width: 1, height: 1 },
    modelVersion: '6.1',
  }

  setContentUrls(urls: string[]): this {
    this.command.contentUrls = urls
    return this
  }

  setPrompt(prompt: string): this {
    this.command.prompt = prompt
    return this
  }

  setAspectRatio(width: number, height: number): this {
    this.command.aspectRatio = { width, height }
    return this
  }

  setModelVersion(version: string): this {
    this.command.modelVersion = version
    return this
  }

  addStyleUrl(url: string): this {
    this.command.styleUrls = this.command.styleUrls || []
    this.command.styleUrls.push(url)
    return this
  }

  addCharacterUrl(url: string): this {
    this.command.characterUrls = this.command.characterUrls || []
    this.command.characterUrls.push(url)
    return this
  }

  addObjectReference(url: string, weight?: number): this {
    this.command.objectReferences = this.command.objectReferences || []
    this.command.objectWeights = this.command.objectWeights || []
    this.command.objectReferences.push(url)
    if (weight !== undefined) {
      this.command.objectWeights.push(weight)
    }
    return this
  }

  setStyleStrength(strength: number): this {
    this.command.styleStrength = strength
    return this
  }

  setChaos(chaos: number): this {
    this.command.chaos = chaos
    return this
  }

  setNegativePrompt(prompt: string): this {
    this.command.negativePrompt = prompt
    return this
  }

  setQuality(quality: number): this {
    this.command.quality = quality
    return this
  }

  setSeed(seed: number): this {
    this.command.seed = seed
    return this
  }

  setRawMode(enabled: boolean): this {
    this.command.rawMode = enabled
    return this
  }

  setVideoMode(enabled: boolean): this {
    this.command.isVideo = enabled
    if (enabled) {
      this.command.fastMode = true // 视频模式强制启用快速模式
    }
    return this
  }

  setMotion(motion: 'low' | 'high'): this {
    this.command.motion = motion
    return this
  }

  setBatchSize(size: 1 | 2 | 4): this {
    this.command.batchSize = size
    return this
  }

  build(): PromptGenerationCommand {
    const result = PromptCommandParser.parse(
      PromptCommandParser.buildCommand(this.command)
    )

    if (!result.success || !result.data) {
      throw new Error(`命令构建失败：${result.errors.join(', ')}`)
    }

    return result.data
  }

  toString(): string {
    return PromptCommandParser.buildCommand(this.command)
  }
}

export const creationScrollToBottom = () => {
  const bottom = document.getElementById('creation-scroll-bottom')

  if (bottom) {
    setTimeout(() => {
      bottom.scrollIntoView(false)
    }, 100)
    const scrollTop = document.getElementById(
      'creation-scroll-container'
    )?.scrollTop
    localStorage.setItem(
      'creation-scroll-position',
      scrollTop?.toString() || '0'
    )
  }
}

export const getJobTypeClass = (type: JobType): GenerationType => {
  switch (type) {
    case JobType.VIDEO_EXTEND:
      return 'video'
    case JobType.VIDEO_GENERATION:
      return 'video'
    case JobType.VIDEO_UPSCALE:
      return 'video'
    case JobType.ARTICLE_GENERATION:
      return 'article'
    default:
      return 'image'
  }
}

export const CrossOriginDownloader = {
  async download(fileUrl: string, customFileName?: string) {
    const fileName =
      customFileName || CrossOriginDownloader.extractFileName(fileUrl)
    // 1. 先尝试 fetch
    try {
      const response = await fetch(fileUrl)
      if (response.ok) {
        const blob = await response.blob()
        saveAs(blob, fileName)
        return
      }
    } catch (error) {
      console.log('Fetch failed, trying anchor method...', error)
    }

    // 2. 降级到 anchor 方法
    CrossOriginDownloader.downloadViaAnchor(fileUrl, fileName)
  },

  downloadViaAnchor(url: string, fileName: string) {
    const a = document.createElement('a')
    a.href = url
    a.download = fileName
    a.target = '_blank' // 防止某些浏览器的问题

    // 某些浏览器需要将元素添加到 DOM
    document.body.appendChild(a)
    a.click()

    // 清理
    setTimeout(() => {
      document.body.removeChild(a)
    }, 100)
  },

  extractFileName(url: string): string {
    const urlParts = url.split('/')
    return urlParts[urlParts.length - 1] || 'download'
  },
}
