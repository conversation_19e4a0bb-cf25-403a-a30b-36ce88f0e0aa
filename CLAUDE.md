# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
- `pnpm dev` - Start dev server with hot reload
- `pnpm build` - Build for production (runs TypeScript compilation + Vite build)
- `pnpm build:analyze` - Build with bundle analysis
- `pnpm lint` - Run Biome linter
- `pnpm lint:fix` - Run Biome linter with auto-fix
- `pnpm format` - Format code with Biome
- `pnpm format:check` - Check code formatting without changes
- `pnpm check` - Run Biome comprehensive check (lint + format)
- `pnpm check:fix` - Run Biome comprehensive check with auto-fix
- `pnpm preview` - Preview production build
- `pnpm preview:build` - Build and preview production build

### Package Management
- Uses `pnpm` as package manager (v10.15.0)
- Node.js with TypeScript and Vite
- **Code Quality**: Biome for linting and formatting (replaces ESLint/Prettier)
- **Web Workers**: Uses Comlink for web worker communication
- **Testing**: No test framework configured (only Biome linting)

### Development Notes
- **TypeScript Compilation**: Required before Vite build (`tsc -b`)
- **Hot Reload**: Vite dev server with SWC for fast compilation
- **Worker Communication**: Comlink for seamless web worker integration
- **Path Aliases**: `@/` maps to `./src/` via Vite config
- **Environment**: Development uses local API at `http://**************:8100/api`, production uses `/api`
- **Code Style**: Biome enforces single quotes for JS, double quotes for JSX, 2-space indentation, 80-char line width

### Import Conventions
- **Import Order**: React/types → Third-party libraries → Absolute imports (`@/...`) → Relative imports
- **Type-only imports**: Use `import type { X } from '...'` to reduce runtime code
- **Path aliases**: Use `@/` instead of relative paths like `../../../`
- **Avoid deep relative imports**: Always prefer absolute imports with `@/` prefix

## Architecture Overview

This is a **React-based AI content generation platform** built with TypeScript, Vite, and Zustand state management, specifically targeting Chinese users with social media optimization features.

### Core Purpose
- **AI-powered content generation**: Images, videos, and articles through multiple APIs
- **Advanced image editing**: Layer-based manipulation with AI assistance and canvas operations
- **Creative workflow management**: Job tracking, batch processing, exports, and history management
- **Social media optimization**: Pre-configured aspect ratios for Chinese platforms (WeChat, Weibo, Xiaohongshu, Douyin)

### State Management Architecture

**Zustand Stores** (9 stores with persistence):
- **Consistent store structure** with strong typing and semantic action names
- **Persistence middleware** using localStorage and IndexedDB via `idb-keyval`
- **Authentication middleware** with API interceptors and automatic 401 redirect handling
- **Complex async state management** for AI job workflows with real-time status updates
- **Layer-based state management** for canvas operations with undo/redo history

**Store Organization:**
- `useCommonStore` - Authentication, user state, global loading, navigation
- `useCreationStore` - Main AI generation engine (1,363 lines) - handles 11 job types
- `useAdvancedEditStore` - Advanced editing features, history management, canvas state
- `useEditStore` - Basic layer management for simple editing
- `useCutBoxStore` - Canvas positioning and viewport management
- `useEstablishingShotStore` - Reference images and style references
- `useForegroundStore` - Layer and foreground management
- `useImageStore` - Image selection and management for image dialog system
- `useTierStore` - Subscription/tier management

### State Management Architecture

**Zustand Stores** (9 stores with persistence):
- **Consistent store structure** with strong typing and semantic action names
- **Persistence middleware** using localStorage and IndexedDB via `idb-keyval`
- **Authentication middleware** with API interceptors and automatic 401 redirect handling
- **Complex async state management** for AI job workflows with real-time status updates
- **Layer-based state management** for canvas operations with undo/redo history
- **Job queue management** with status tracking and polling mechanisms

### Key Directories

#### `/src/routes/` - Application Pages (React Router v7)
- `/` - Home (main creation interface)
- `/creation/:jobid` - Job detail and editing interface
- `/advanced_edit` - Advanced editing with history management
- `/advanced_edit_new` - Sophisticated image editing with canvas
- `/login` - Authentication flow
- `/product` - Product showcase
- `/product/:id` - Product detail pages

#### `/src/components/` - UI Components
- **UI/** - shadcn/ui design system (40+ Radix UI components)
- **Creation components** - Job generation interface, input forms, job management
- **Advanced editing** - Canvas-based editing tools, layer panels, toolbars
- **Upload components** - File upload, drag-and-drop, image processing

#### Project Structure Conventions
- **Entry points**: `index.html` → `src/main.tsx` → route assembly in `src/routes/`
- **Component placement**: New components in `src/components/`, page-level components in `src/routes/`
- **State management**: All global/business state in `src/stores/` with strong typing
- **Route data loading**: Route-related data requests in `src/loader/` with React Router v7 `loader` functions
- **Build artifacts**: Manual chunking strategy in `vite.config.ts` for optimal loading performance

#### `/src/stores/` - Zustand State Management (9 stores)
All stores use persistence middleware with localStorage/IndexedDB:
- `useCommonStore` - Authentication, user state, global loading, navigation
- `useCreationStore` - Main AI generation engine (1,363 lines) - handles 11 job types
- `useAdvancedEditStore` - Advanced editing features, history management, canvas state
- `useEditStore` - Basic layer management for simple editing
- `useCutBoxStore` - Canvas positioning and viewport management
- `useEstablishingShotStore` - Reference images and style references
- `useForegroundStore` - Layer and foreground management
- `useImageStore` - Image selection and management for image dialog system
- `useTierStore` - Subscription/tier management

### Component Architecture

**Layered Component Structure:**
- **Page-level components** in `src/routes/` (React Router v7)
- **Feature components** in `src/components/` (business logic)
- **UI components** in `src/components/ui/` (shadcn/ui design system)

**Key Architectural Components:**
- **`AdvancedEditInput`** - Sophisticated input with search integration and command parsing
- **`ImageDialog`** - Global image selection system with accessibility support
- **`MasonryGrid`** - Optimized layout with ahooks debouncing (min 128px height)
- **`CreationBox`** - Job management interface with real-time status updates
- **`AdvancedLayerList`** - Layer manipulation tools with history management

**shadcn/ui Design System:**
- Built on 40+ Radix UI components with Tailwind CSS
- Variant-based styling with `class-variance-authority`
- Accessibility-first approach with proper ARIA labels
- Dark mode support via `next-themes`

### Performance and Worker Architecture

**Web Workers with Comlink:**
- Heavy canvas operations offloaded to workers using Comlink
- Type-safe communication between main thread and workers
- Performance optimization to prevent UI blocking
- Located in `src/workers/` with Vite plugin configuration

**Key Optimizations:**
- Manual chunking strategy in `vite.config.ts` for optimal loading
- Debounced operations using ahooks utilities (`useDebounceEffect`, `useDebounceFn`)
- Canvas operations in separate workers
- Lazy loading for heavy components
- Blob URL management with proper cleanup

#### `/src/config/index.ts` - Configuration
- **API endpoints**: Development API at `http://**************:8100/api`, production uses `/api`
- **Midjourney API**: `https://api.fotomore.com/api/creations/v1`
- **Social media presets**: 15 pre-configured aspect ratios for Chinese platforms
- **Image proportions**: 8 standard ratios + 15 social media optimized ratios

#### `/src/workers/` - Web Workers
- `canva.ts` - Canvas operations using Comlink for performance optimization
- **Comlink Integration**: Web workers use Comlink for seamless type-safe communication
- **Performance**: Heavy canvas operations offloaded to workers to prevent UI blocking
- **Configuration**: Worker plugins configured in `vite.config.ts` with Comlink support

#### `/src/loader/` - Data Loaders (React Router v7)
- `advanced_edit.ts` - Advanced edit page data loading
- `creation_detail.ts` - Creation detail page data loading
- `product_detail.ts` - Product detail page data loading

### AI Generation System

**Job Types (11 types):**
- **Image Generation**: Default, variation, upscale, remix, pan, outpaint
- **Video Generation**: Generation, extend, upscale
- **Article Generation**: AI-powered content creation
- **Background Removal**: AI-powered image processing

**Reference System:**
- Style references for consistent visual aesthetics
- Content references for composition guidance
- Face references for character consistency

### Technology Stack
- **Frontend**: React 18, TypeScript, Vite with SWC
- **Styling**: Tailwind CSS, shadcn/ui (Radix UI + Tailwind)
- **State**: Zustand with persistence middleware
- **Routing**: React Router v7 with data loaders
- **Build**: Vite with SWC for fast compilation
- **Package Manager**: pnpm v10.15.0 with workspace support
- **UI Library**: 40+ Radix UI components via shadcn/ui
- **Icons**: Lucide React
- **Date**: dayjs with Chinese locale
- **HTTP**: ky with authentication interceptors
- **Form**: react-hook-form with zod resolvers
- **Theme**: next-themes for dark mode
- **Workers**: Comlink for web worker communication
- **React hooks**: ahooks for enhanced React utilities
- **Charts**: Recharts for data visualization
- **File operations**: file-saver, dom-to-image for exports
- **Image processing**: Canvas API with web worker optimization
- **Utilities**: radash, lodash for data manipulation
- **Drag & Drop**: @xyflow/react for canvas editing workflows
- **Command Parsing**: Custom PromptCommandParser for advanced input processing and URL extraction
- **Search Integration**: Enhanced search capabilities with query processing and result optimization

### Key Features
- **Multi-modal AI generation**: Image, video, and article generation
- **Layer-based editing**: Advanced image manipulation with canvas operations
- **Reference system**: Style, content, and face references for consistency
- **Social media optimization**: Pre-configured for Chinese platforms
- **Real-time status tracking**: WebSocket-like job status updates
- **Export capabilities**: High-quality downloads with cross-origin handling
- **Advanced canvas editing**: Layer management, filters, transformations
- **Batch processing**: Multiple jobs with progress tracking

### Development Notes
- **Language**: Chinese UI with English code comments
- **Environment**: Production API configured by default
- **Code Quality**: Uses Biome for linting, formatting, and comprehensive checks
- **Testing**: No test framework configured (only Biome linting)
- **Fonts**: Custom Chinese fonts in `/public/fonts/`
- **Workers**: Uses Comlink for web worker communication in `/src/workers/`
- **Performance**: Web workers for heavy computations, optimized image processing
- **Build**: TypeScript compilation followed by Vite build for production
- **Persistence**: Zustand stores with localStorage for user preferences, IndexedDB for larger data
- **Authentication**: Token-based with automatic API interceptors and 401 redirect handling
- **Error Handling**: Centralized error management with toast notifications
- **Image Processing**: Canvas API with transparency detection and export capabilities
- **Social Media**: Pre-configured aspect ratios for WeChat, Weibo, Xiaohongshu, Douyin
- **Path Aliases**: `@/` maps to `./src/` via TypeScript and Vite configuration
- **Package Management**: Uses `pnpm` v10.15.0 with workspace support
- **Code Style**: Biome enforces single quotes for JS, double quotes for JSX, 2-space indentation, 80-char line width
- **Build Optimization**: Manual chunking strategy in `vite.config.ts` groups dependencies by category (react-vendor, ui-vendor, utils-vendor, etc.)
- **Cross-Origin Handling**: Custom utilities for downloading and processing images from different domains
- **Chinese Localization**: UI in Chinese with English comments, dayjs configured for Chinese locale
- **Web Workers**: Canvas operations offloaded to workers using Comlink for performance
- **Error Handling**: Centralized error management with toast notifications via Sonner
- **Form Validation**: react-hook-form with zod resolvers for type-safe form handling

### Current Development Focus
- **Active Branch**: `feature/input_pull`
- **Recent Features**: Advanced search functionality, input pulling, PromptCommandParser integration, image dialog system, masonry grid optimization
- **Search System**: Enhanced search with query logic and improved result display in advanced editing components
- **Command Parsing**: PromptCommandParser for advanced input processing with URL extraction, parameter parsing, and validation
- **Key Components**:
  - `AdvancedEditInput` - Input component with search integration
  - `AdvancedEditTools` - Enhanced editing tools with search capabilities
  - `ImageDialog` - Image selection and management dialog (with proper accessibility support)
  - `MasonryGrid` - Optimized masonry layout with ahooks debounce and minimum height constraints
  - Cross-origin image downloading and processing utilities
  - `SearchBox` - Enhanced search interface with result optimization

### Recent Technical Improvements
- **Masonry Grid**: Implemented ahooks-based debouncing for layout calculations, ensuring items have minimum height of 128px
- **Accessibility**: Fixed Radix UI Dialog components to include proper `DialogTitle` and `DialogDescription` for screen readers
- **Performance**: Replaced manual debounce implementations with ahooks utilities (`useDebounceEffect`, `useDebounceFn`)
- **State Management**: Enhanced input pulling and search integration across advanced editing components

## Code Quality Configuration

**Biome Configuration** (`biome.json`):
- **Import conventions**: Absolute imports using `@/`, type-only imports when needed
- **Code formatting**: Single quotes for JS, double quotes for JSX, 2-space indentation, 80-char line width
- **Linting rules**: Comprehensive rules for accessibility, complexity, correctness, performance, security
- **Project structure**: Follows React + TypeScript patterns with Zustand state management
- **UI patterns**: shadcn/ui component library with Tailwind CSS atomic styling

**Cursor Rules Integration**: Comprehensive Cursor rules in `.cursor/rules/` directory covering:
- **Import conventions**: Absolute imports using `@/`, type-only imports when needed
- **Project structure**: Component placement, routing, and state management patterns
- **State and UI**: Zustand store patterns and shadcn/ui component usage
- **Tailwind usage**: Atomic styling and theme system guidelines
- **Workers and Comlink**: Web worker communication patterns

## Important Instructions
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.