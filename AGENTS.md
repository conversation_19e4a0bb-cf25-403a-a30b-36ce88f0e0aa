# Repository Guidelines

## Project Structure & Module Organization
The entry point is `src/main.tsx`. Routes, loaders, and any data-fetching helpers live in `src/routes/` and `src/loader/`, while shared UI components reside in `src/components/`. Global stores sit in `src/stores/`, custom hooks in `src/hooks/`, and feature utilities or configuration in `src/lib/` and `src/config/`. Bundle images, fonts, and other assets through `src/assets/`; ship unprocessed static files from `public/`. Worker scripts belong in `src/workers/`; reuse the existing Comlink setup. Adjust global styles in `src/index.css`.

## Build, Test, and Development Commands
- `pnpm install` — install dependencies using pnpm v10.15.0.
- `pnpm dev` — start the Vite dev server.
- `pnpm build` — produce the production bundle; run `pnpm build:analyze` for bundle size insights.
- `pnpm preview` — serve the build locally for smoke testing.
- `pnpm lint` / `pnpm lint:fix` — run Biome checks and optionally apply safe fixes.
- `pnpm format` and `pnpm check` — enforce formatting and run the aggregate quality gate before opening a PR.

## Coding Style & Naming Conventions
Biome enforces 2-space indentation, 80-character lines, single quotes in TS/JS, and double quotes in JSX. Order imports as React → third-party → `@/` aliases → relative paths, and mark type-only imports with `type`. Use camelCase for functions and variables, PascalCase for components, types, and Zustand stores, and kebab-case for filenames. Keep Tailwind classes atomic; pair light/dark variants (e.g., `bg-muted dark:bg-muted/80`) where applicable. Exported APIs must declare explicit types to satisfy strict TypeScript.

## Testing Guidelines
There is no automated test suite. Rely on `pnpm check` to catch lint and type regressions, then manually verify core journeys: navigation, advanced search, and image dialogs. When adding features, document manual test steps or attach short clips so reviewers can reproduce results quickly.

## Commit & Pull Request Guidelines
Use Conventional Commits (`type(scope): summary`) in English or Chinese, such as `feat(routes): add gallery filters` or `chore(依赖): 升级 pnpm`. Rebase instead of merging before pushing. Each PR should state intent, link related issues, list the commands run (at minimum `pnpm check`), and attach screenshots or videos for UI updates. Call out store schema, worker, or configuration changes to help reviewers focus.

## Security & Configuration Tips
Expose client-side secrets with the `VITE_` prefix only. Keep configuration changes localized to `vite.config.ts`, `tailwind.config.ts`, or `src/config/`, and update `README.md` when introducing new flags. Persist user preferences through well-named Zustand actions so other contributors understand stored keys.
