# Repository Guidelines

## Project Structure & Module Organization
Source lives under `src/`, with `src/main.tsx` bootstrapping the app. Route handlers and loaders sit in `src/routes/` and `src/loader/`; shared UI pieces belong in `src/components/`. Keep global state in `src/stores/`, reusable hooks in `src/hooks/`, and domain helpers or configuration in `src/lib/` and `src/config/`. Ship processed assets from `src/assets/` and static files from `public/`. Workers belong in `src/workers/`, using the existing Comlink wiring. Adjust global styles in `src/index.css`.

## Build, Test, and Development Commands
Run `pnpm install` (pnpm v10.15.0) before hacking. `pnpm dev` starts the Vite dev server, while `pnpm build` emits the production bundle; use `pnpm build:analyze` for bundle inspection. `pnpm preview` serves the built assets locally. Quality gates: `pnpm lint` or `pnpm lint:fix` for Biome, `pnpm format` to apply formatting, and `pnpm check` for the full lint + type suite.

## Coding Style & Naming Conventions
Biome enforces 2-space indentation, 80-character lines, single quotes for TS/JS, and double quotes in JSX. Order imports React → third-party → `@/` aliases → relative paths, marking type-only imports with `type`. Follow camelCase for functions/variables, PascalCase for components, types, and Zustand stores, and kebab-case filenames. Keep Tailwind classes atomic and pair light/dark variants (e.g., `bg-muted dark:bg-muted/80`). Exported APIs must declare explicit TypeScript types.

## Testing Guidelines
There is no automated testing harness. Run `pnpm check` before every PR to catch lint and type regressions. Manually verify critical flows: navigating between routes, executing advanced search, and opening image dialogs. Document manual test steps or attach short clips when shipping new behavior.

## Commit & Pull Request Guidelines
Use Conventional Commits (`type(scope): summary`), in English or Chinese—for example `feat(routes): add gallery filters` or `chore(依赖): 升级 pnpm`. Rebase before pushing. PRs must state intent, link related issues, list executed commands (at minimum `pnpm check`), and include screenshots or recordings for UI updates. Highlight changes that touch stores, workers, or configuration files to focus reviews.

## Security & Configuration Tips
Expose browser-visible secrets with the `VITE_` prefix only. Confine configuration tweaks to `vite.config.ts`, `tailwind.config.ts`, or modules under `src/config/`, and update `README.md` when adding flags. Persist user preferences through clearly named Zustand actions so contributors understand stored keys.
